package com.bees360.flink.entity;

import static com.bees360.flink.entity.Functions.acceptIfNotEmpty;
import static com.bees360.flink.entity.Functions.acceptIfNotNull;
import static com.bees360.flink.entity.Functions.acceptIfNotNullWithConvert;
import static com.bees360.flink.entity.Utils.format;

import com.google.gson.Gson;

import lombok.Data;

import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class Activity {
    private static final Logger LOG = LoggerFactory.getLogger(Activity.class);
    private static final Gson gson = new Gson();

    private String objectId;
    private long projectId;
    private String action;
    private Timestamp createdAt;
    private String createdBy;
    private String createdByUser;
    private String entityId;
    private String entityType;
    private String entityName;
    private int entityCount;
    private String fieldName;
    private String fieldDisplayName;
    private String fieldType;
    private String value;
    private String oldValue;
    private String comment;
    private String oldComment;
    private String source;
    private String level;
    private String projectType;
    private String pilot;
    private String[] allSyncLog;
    private String[] extraVisiblePlatform;
    private String[] blockedPlatform;
    private String[] visibility;
    private boolean deleted;

    public static Activity fromDocument(Document doc) {
        var activity = new Activity();
        acceptIfNotNullWithConvert(activity::setObjectId, doc.get("_id"), Object::toString);
        acceptIfNotNullWithConvert(
                activity::setProjectId,
                doc.get("projectId"),
                id -> {
                    try {
                        return Long.parseLong(id.toString());
                    } catch (NumberFormatException e) {
                        LOG.error(
                                "Fail to get project id of activity {} (skip): {}",
                                activity.getObjectId(),
                                e);
                        return null;
                    }
                });
        acceptIfNotNull(activity::setAction, doc.getString("action"));
        acceptIfNotNullWithConvert(
                activity::setCreatedAt,
                doc.getDate("createdAt"),
                (date) -> Timestamp.from(date.toInstant()));
        acceptIfNotNullWithConvert(activity::setCreatedBy, doc.get("createdBy"), Object::toString);
        acceptIfNotNullWithConvert(
                activity::setCreatedByUser,
                doc.get("createdByUser"),
                (u) -> {
                    User user = User.fromDocument(activity.getObjectId(), u);
                    if (user == null) {
                        return null;
                    }
                    return gson.toJson(user);
                });
        acceptIfNotNull(activity::setEntityId, doc.getString("entityId"));
        acceptIfNotNull(activity::setEntityType, doc.getString("entityType"));
        acceptIfNotNull(activity::setEntityName, doc.getString("entityName"));
        acceptIfNotNullWithConvert(
                activity::setEntityCount,
                doc.get("entityCount"),
                count -> {
                    try {
                        return Integer.parseInt(count.toString());
                    } catch (NumberFormatException e) {
                        LOG.error(
                                "Fail to get entity count of activity {} (skip): {}",
                                activity.getObjectId(),
                                e);
                        return null;
                    }
                });
        acceptIfNotNull(activity::setFieldName, doc.getString("filedName"));
        acceptIfNotNull(activity::setFieldDisplayName, doc.getString("fieldDisplayName"));
        acceptIfNotNull(activity::setFieldType, doc.getString("filedType"));
        acceptIfNotNull(activity::setValue, doc.getString("value"));
        acceptIfNotNull(activity::setOldValue, doc.getString("oldValue"));

        acceptIfNotNullWithConvert(
                activity::setComment,
                doc.get("comment"),
                (c) -> Activity.convertActivityComment(activity.getObjectId(), c));
        acceptIfNotNullWithConvert(
                activity::setOldComment,
                doc.get("oldComment"),
                (c) -> Activity.convertActivityComment(activity.getObjectId(), c));

        acceptIfNotNull(activity::setSource, doc.getString("source"));
        acceptIfNotNull(activity::setLevel, doc.getString("level"));
        acceptIfNotNull(activity::setProjectType, doc.getString("projectType"));
        acceptIfNotNullWithConvert(
                activity::setPilot,
                doc.get("pilot"),
                (p) -> {
                    var pilot = Pilot.fromDocument(activity.getObjectId(), p);
                    if (pilot == null) {
                        return null;
                    }
                    return gson.toJson(pilot);
                });
        acceptIfNotNullWithConvert(
                activity::setAllSyncLog,
                doc.getList("allSyncLog", Object.class),
                obj ->
                        obj.stream()
                                .map(a -> ActivitySyncLog.fromDocument(activity.getObjectId(), a))
                                .map(gson::toJson)
                                .toArray(String[]::new));
        acceptIfNotNullWithConvert(
                activity::setExtraVisiblePlatform,
                doc.getList("extraVisiblePlatform", String.class),
                obj -> obj.toArray(String[]::new));
        acceptIfNotNullWithConvert(
                activity::setBlockedPlatform,
                doc.getList("blockedPlatform", String.class),
                obj -> obj.toArray(String[]::new));
        acceptIfNotNullWithConvert(
                activity::setVisibility,
                doc.getList("visibility", String.class),
                obj -> obj.toArray(String[]::new));
        activity.setDeleted(doc.getBoolean("deleted", false));
        return activity;
    }

    private static String convertActivityComment(String identifier, Object commentObj) {
        var comment = NestedComment.fromDocument(identifier, commentObj);
        if (comment == null) {
            return null;
        }
        return gson.toJson(comment);
    }

    @Data
    static class NestedComment {
        private String id;
        private String projectId;
        private String content;
        private String replyTo;
        private User createdBy;
        private String createdAt;
        private String updatedAt;
        private String source;
        private String contentType;
        private List<Comment.Attachment> attachment;
        private String richText;
        private String topic;

        public static NestedComment fromDocument(String identifier, Object commentObj) {
            if (commentObj == null) {
                return null;
            }
            if (commentObj instanceof Document doc) {
                var comment = new NestedComment();
                acceptIfNotNullWithConvert(comment::setId, doc.get("_id"), Object::toString);
                acceptIfNotNullWithConvert(
                        comment::setProjectId, doc.get("projectId"), Object::toString);
                acceptIfNotEmpty(comment::setContent, doc.getString("content"));
                acceptIfNotEmpty(comment::setReplyTo, doc.getString("replyTo"));
                acceptIfNotNullWithConvert(
                        comment::setCreatedBy,
                        doc.getString("createdBy"),
                        (id) -> {
                            var user = new User();
                            user.setId(id);
                            return user;
                        }
                );
                acceptIfNotNullWithConvert(
                        comment::setCreatedBy,
                        doc.get("createdByUser"),
                        (u) -> User.fromDocument(comment.getId(), u));
                acceptIfNotNullWithConvert(
                        comment::setCreatedAt,
                        doc.getDate("createdAt"),
                        (date) -> format(date.toInstant()));
                acceptIfNotNullWithConvert(
                        comment::setUpdatedAt,
                        doc.getDate("updatedAt"),
                        (date) -> format(date.toInstant()));
                acceptIfNotEmpty(comment::setSource, doc.getString("source"));
                acceptIfNotEmpty(comment::setContentType, doc.getString("contentType"));
                acceptIfNotNullWithConvert(
                        comment::setAttachment,
                        doc.getList("attachment", Object.class),
                        obj ->
                                obj.stream()
                                        .map(
                                                a ->
                                                        Comment.Attachment.fromDocument(
                                                                comment.getId(), a))
                                        .collect(Collectors.toList()));
                acceptIfNotEmpty(comment::setRichText, doc.getString("richText"));
                acceptIfNotEmpty(comment::setTopic, doc.getString("topic"));
                return comment;
            } else {
                LOG.error(
                        "Skip to set comment because it is not a valid comment: activity id {},"
                            + " comment {}",
                        identifier,
                        commentObj);
                return null;
            }
        }
    }

    @Data
    static class ActivitySyncLog {
        private String activityId;
        private String syncTo;
        private String status;
        private User createdBy;
        private String createdAt;
        private String updatedAt;

        public static ActivitySyncLog fromDocument(String identifier, Object logObj) {
            if (logObj == null) {
                return null;
            }
            if (logObj instanceof Document doc) {
                var log = new ActivitySyncLog();
                acceptIfNotEmpty(log::setActivityId, doc.getString("activityId"));
                acceptIfNotEmpty(log::setSyncTo, doc.getString("syncTo"));
                acceptIfNotEmpty(log::setStatus, doc.getString("status"));
                acceptIfNotNullWithConvert(
                        log::setCreatedBy,
                        doc.getString("createdBy"),
                        id -> {
                            var user = new User();
                            user.setId(id);
                            return user;
                        });
                acceptIfNotNullWithConvert(
                        log::setCreatedAt,
                        doc.getDate("createdAt"),
                        (date) -> format(date.toInstant()));
                acceptIfNotNullWithConvert(
                        log::setUpdatedAt,
                        doc.getDate("updatedAt"),
                        (date) -> format(date.toInstant()));
                return log;
            }
            LOG.error(
                    "Skip to set activity sync log because it is not valid: id {}, log {}",
                    identifier,
                    logObj);
            return null;
        }
    }

    @Data
    static class Pilot {
        private User operationsManager;
        private User pilotUser;

        public static Pilot fromDocument(String identifier, Object pilotObj) {
            if (pilotObj == null) {
                return null;
            }
            if (pilotObj instanceof Document doc) {
                var pilot = new Pilot();
                acceptIfNotNull(
                        pilot::setOperationsManager,
                        User.fromDocument(identifier, doc.get("operationsManager")));
                acceptIfNotNull(
                        pilot::setPilotUser, User.fromDocument(identifier, doc.get("pilotUser")));
                return pilot;
            } else {
                LOG.error(
                        "Skip to set activity pilot because it is not valid: id {}, pilot {}",
                        identifier,
                        pilotObj);
                return null;
            }
        }
    }
}
