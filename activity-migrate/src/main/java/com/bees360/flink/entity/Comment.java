package com.bees360.flink.entity;

import static com.bees360.flink.entity.Functions.acceptIfNotEmpty;
import static com.bees360.flink.entity.Functions.acceptIfNotNull;
import static com.bees360.flink.entity.Functions.acceptIfNotNullWithConvert;
import static com.bees360.flink.entity.Utils.format;

import com.google.gson.Gson;

import lombok.Data;

import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;

@Data
public class Comment {
    private static final Logger LOG = LoggerFactory.getLogger(Comment.class);
    private static final Gson gson = new Gson();

    private String objectId;
    private long projectId;
    private String content;
    private String replyTo;
    private Timestamp createdAt;
    private String createdBy;
    private String createdByUser;
    private Timestamp updatedAt;
    private String source;
    private String contentType;
    private String[] attachment;
    private String richText;
    private String topic;
    private boolean deleted;

    public static Comment fromDocument(Document doc) {
        var comment = new Comment();
        acceptIfNotNullWithConvert(comment::setObjectId, doc.get("_id"), Object::toString);
        acceptIfNotNullWithConvert(
                comment::setProjectId,
                doc.get("projectId"),
                id -> {
                    try {
                        return Long.parseLong(id.toString());
                    } catch (NumberFormatException e) {
                        LOG.error(
                                "Fail to get project id of activity {} (skip): {}",
                                comment.getObjectId(),
                                e);
                        return -1L;
                    }
                });
        acceptIfNotNull(comment::setContent, doc.getString("content"));
        acceptIfNotNull(comment::setReplyTo, doc.getString("replyTo"));
        acceptIfNotNullWithConvert(
                comment::setCreatedAt,
                doc.getDate("createdAt"),
                (date) -> Timestamp.from(date.toInstant()));
        acceptIfNotNull(comment::setCreatedBy, doc.getString("createdBy"));
        acceptIfNotNullWithConvert(
                comment::setCreatedByUser,
                doc.get("createdByUser"),
                (u) -> {
                    User user = User.fromDocument(comment.getObjectId(), u);
                    if (user == null) {
                        return null;
                    }
                    return gson.toJson(user);
                });
        acceptIfNotNullWithConvert(
                comment::setUpdatedAt,
                doc.getDate("updatedAt"),
                (date) -> Timestamp.from(date.toInstant()));
        acceptIfNotNull(comment::setSource, doc.getString("source"));
        acceptIfNotNull(comment::setContentType, doc.getString("contentType"));
        acceptIfNotNullWithConvert(
                comment::setAttachment,
                doc.getList("attachment", Object.class),
                obj ->
                        obj.stream()
                                .map(a -> Attachment.fromDocument(comment.getObjectId(), a))
                                .map(gson::toJson)
                                .toArray(String[]::new));
        acceptIfNotNull(comment::setRichText, doc.getString("richText"));
        acceptIfNotNull(comment::setTopic, doc.getString("topic"));
        comment.setDeleted(doc.getBoolean("deleted", false));
        return comment;
    }

    @Data
    static class Attachment {
        private String url;
        private String filename;
        private ResourceMetadata metadata;

        public static Attachment fromDocument(String identifier, Object attachmentObj) {
            if (attachmentObj == null) {
                return null;
            }
            if (attachmentObj instanceof Document doc) {
                var attachment = new Attachment();
                acceptIfNotEmpty(attachment::setUrl, doc.getString("url"));
                acceptIfNotEmpty(attachment::setFilename, doc.getString("filename"));
                acceptIfNotNull(
                        attachment::setMetadata,
                        ResourceMetadata.fromDocument(identifier, doc.get("metadata")));
                return attachment;
            }
            LOG.error(
                    "Skip to set comment attachment because it is not valid: id {}, attachment {}",
                    identifier,
                    attachmentObj);
            return null;
        }
    }

    @Data
    static class ResourceMetadata {
        private String contentLength;
        private String contentType;
        private String contentMD5;
        private String eTag;
        private String lastModified;

        public static ResourceMetadata fromDocument(String identifier, Object metadataObj) {
            if (metadataObj == null) {
                return null;
            }
            if (metadataObj instanceof Document doc) {
                var metadata = new ResourceMetadata();
                Functions.acceptIfNotNullWithConvert(
                        metadata::setContentLength, doc.get("contentLength"), Object::toString);
                acceptIfNotEmpty(metadata::setContentType, doc.getString("contentType"));
                acceptIfNotEmpty(metadata::setContentMD5, doc.getString("contentMD5"));
                acceptIfNotEmpty(metadata::setETag, doc.getString("eTag"));
                acceptIfNotNullWithConvert(
                        metadata::setLastModified,
                        doc.getDate("lastModified"),
                        date -> format(date.toInstant()));
                return metadata;
            }
            LOG.error(
                    "Skip to set comment attachment resource because it is not valid: id {},"
                        + " attachment {}",
                    identifier,
                    metadataObj);
            return null;
        }
    }
}
