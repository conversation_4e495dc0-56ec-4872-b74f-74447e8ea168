package com.bees360.flink.entity;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

public class Functions {
    /**
     * If value is not null, call the setter
     *
     * @param consumer Function that uses the value
     * @param value The value to be judged and used
     * @param <T> value type
     */
    public static <T> void acceptIfNotNull(Consumer<T> consumer, T value) {
        if (Objects.nonNull(value)) {
            consumer.accept(value);
        }
    }

    public static <T> void acceptIfNotEmpty(Consumer<T> consumer, T value) {
        if (Objects.nonNull(value) && StringUtils.isNotEmpty(value.toString())) {
            consumer.accept(value);
        }
    }

    /**
     * @param consumer function that uses the value
     * @param value The value to be judged and used
     * @param convert value conversion function
     * @param <A> original value type
     * @param <T> The converted value type, provided to the setter function
     */
    public static <A, T> void acceptIfNotNullWithConvert(
            Consumer<T> consumer, A value, Function<A, T> convert) {
        if (Objects.nonNull(value)) {
            T convertValue = convert.apply(value);
            if (Objects.isNull(convertValue)) {
                return;
            }
            consumer.accept(convert.apply(value));
        }
    }
}
