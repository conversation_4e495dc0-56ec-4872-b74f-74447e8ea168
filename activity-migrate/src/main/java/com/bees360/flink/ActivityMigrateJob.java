package com.bees360.flink;

import com.bees360.flink.entity.Activity;
import com.bees360.flink.entity.Comment;
import com.bees360.flink.util.EnvUtils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.connector.jdbc.JdbcSink;
import org.apache.flink.connector.mongodb.source.MongoSource;
import org.apache.flink.connector.mongodb.source.reader.deserializer.MongoDeserializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.bson.BsonDocument;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.util.Arrays;

public class ActivityMigrateJob {
    private static final Logger LOG = LoggerFactory.getLogger(ActivityMigrateJob.class);

    public static void main(String[] args) throws Exception {
        var props = EnvUtils.getProperties(args);

        var connectionOptions = new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
                .withUrl(props.ACT_PG_URL)
                .withDriverName("org.postgresql.Driver")
                .withUsername(props.ACT_PG_USER)
                .withPassword(props.ACT_PG_PASSWORD)
                .build();
        var executionOptions = JdbcExecutionOptions.builder()
                .withBatchSize(1000)
                .withBatchIntervalMs(5000)    // retry interval
                .withMaxRetries(3)            // retry count
                .build();

        var actIdList =
                Arrays.stream(props.ACT_ACTIVITY_IDS.split(",")).filter(id -> !id.isEmpty()).toList();
        var commentIdList = Arrays.stream(props.ACT_COMMENT_IDS.split(",")).filter(id -> !id.isEmpty()).toList();
        boolean fullMigrate = CollectionUtils.isEmpty(actIdList) && CollectionUtils.isEmpty(commentIdList);

        LOG.info("Start to migrate activity");
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);

        // 1. migrate activity using Flink MongoDB connector
        // 1.1 source for activity
        MongoSource<Document> activityMongoSource =
                MongoSource.<Document>builder()
                        .setUri(props.MONGO_URL)
                        .setDatabase(props.MONGO_DATABASE)
                        .setCollection("activity")
                        .setDeserializationSchema(new MongoDeserializationSchema<>() {
                            @Override
                            public TypeInformation<Document> getProducedType() {
                                return TypeInformation.of(Document.class);
                            }

                            @Override
                            public Document deserialize(BsonDocument bsonDocument) throws IOException {
                                return Document.parse(bsonDocument.toJson());
                            }
                        })
                        .build();

        // 1.2 stream for activity
        DataStream<Activity> actStream =
                env.fromSource(
                                activityMongoSource,
                                WatermarkStrategy.noWatermarks(),
                                "Activity Mongo Source")
                        .map(Activity::fromDocument);
        if (!fullMigrate) {
            // if not initial full migrate
            actStream = actStream.filter(activity -> actIdList.contains(activity.getObjectId()));
        }

        // 1.3 activity sink
        var actSql = """
            INSERT INTO activity (
                object_id, project_id, action, created_at, created_by, created_by_user, entity_id, entity_type, entity_name, entity_count,
                field_name, field_display_name, field_type, value, old_value, comment, old_comment, source, level, project_type, pilot,
                all_sync_log, extra_visible_platform, blocked_platform, visibility, deleted
            ) VALUES (
                ?, ?, ?, ?, ?, ?::jsonb, ?, ?, ?, ?,
                ?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?, ?, ?, ?::jsonb,
                ?::jsonb[], ?::varchar[], ?::varchar[], ?::varchar[], ?
            )
            ON CONFLICT (object_id)
        """ + (fullMigrate? """
            DO NOTHING
        """ : """
            DO UPDATE SET
                project_id = EXCLUDED.project_id, action = EXCLUDED.action, created_at = EXCLUDED.created_at, created_by = EXCLUDED.created_by, created_by_user = EXCLUDED.created_by_user,
                entity_id = EXCLUDED.entity_id, entity_type = EXCLUDED.entity_type, entity_name = EXCLUDED.entity_name, entity_count = EXCLUDED.entity_count,
                field_name = EXCLUDED.field_name, field_display_name = EXCLUDED.field_display_name, field_type = EXCLUDED.field_type, value = EXCLUDED.value, old_value = EXCLUDED.old_value,
                comment = EXCLUDED.comment, old_comment = EXCLUDED.old_comment, source = EXCLUDED.source, level = EXCLUDED.level, project_type = EXCLUDED.project_type, pilot = EXCLUDED.pilot,
                all_sync_log = EXCLUDED.all_sync_log, extra_visible_platform = EXCLUDED.extra_visible_platform, blocked_platform = EXCLUDED.blocked_platform, visibility = EXCLUDED.visibility, deleted = EXCLUDED.deleted;
        """
        );
        actStream.addSink(JdbcSink.sink(
            actSql,
            (statement, activity) -> {
                int paramIndex = 1;
                statement.setString(paramIndex++, activity.getObjectId());
                statement.setLong(paramIndex++, activity.getProjectId());
                statement.setString(paramIndex++, activity.getAction());
                statement.setTimestamp(paramIndex++, activity.getCreatedAt());
                statement.setString(paramIndex++, activity.getCreatedBy());
                statement.setString(paramIndex++, activity.getCreatedByUser()); // jsonb

                statement.setString(paramIndex++, activity.getEntityId());
                statement.setString(paramIndex++, activity.getEntityType());
                statement.setString(paramIndex++, activity.getEntityName());
                statement.setInt(paramIndex++, activity.getEntityCount());

                statement.setString(paramIndex++, activity.getFieldName());
                statement.setString(paramIndex++, activity.getFieldDisplayName());
                statement.setString(paramIndex++, activity.getFieldType());

                statement.setString(paramIndex++, activity.getValue());
                statement.setString(paramIndex++, activity.getOldValue());
                statement.setString(paramIndex++, activity.getComment()); // jsonb
                statement.setString(paramIndex++, activity.getOldComment()); // jsonb

                statement.setString(paramIndex++, activity.getSource());
                statement.setString(paramIndex++, activity.getLevel());
                statement.setString(paramIndex++, activity.getProjectType());
                statement.setString(paramIndex++, activity.getPilot()); // jsonb

                Connection conn = statement.getConnection();
                statement.setArray(paramIndex++, conn.createArrayOf("jsonb", activity.getAllSyncLog()));
                statement.setArray(paramIndex++, conn.createArrayOf("varchar", activity.getExtraVisiblePlatform()));
                statement.setArray(paramIndex++, conn.createArrayOf("varchar", activity.getBlockedPlatform()));
                statement.setArray(paramIndex++, conn.createArrayOf("varchar", activity.getVisibility()));

                statement.setBoolean(paramIndex, activity.isDeleted());
            },
            executionOptions,
            connectionOptions
        ));

        // 2. migrate comment using Flink MongoDB connector
        // 2.1 source for comment
        MongoSource<Document> commentMongoSource =
                MongoSource.<Document>builder()
                        .setUri(props.MONGO_URL)
                        .setDatabase(props.MONGO_DATABASE)
                        .setCollection("comment")
                        .setDeserializationSchema(new MongoDeserializationSchema<>() {
                            @Override
                            public TypeInformation<Document> getProducedType() {
                                return TypeInformation.of(Document.class);
                            }

                            @Override
                            public Document deserialize(BsonDocument bsonDocument) throws IOException {
                                return Document.parse(bsonDocument.toJson());
                            }
                        })
                        .build();

        // 2.2 stream for comment
        DataStream<Comment> commentStream =
                env.fromSource(
                                commentMongoSource,
                                WatermarkStrategy.noWatermarks(),
                                "Comment Mongo Source")
                        .map(Comment::fromDocument);

        if (!fullMigrate) {
            // if not initial full migrate
            commentStream = commentStream.filter(comment -> commentIdList.contains(comment.getObjectId()));
        }

        // 2.3 sink comment
        var commentSql = """
            INSERT INTO comment (object_id, project_id, content, reply_to, created_at, created_by, created_by_user, updated_at, source, content_type, attachment, rich_text, topic, deleted)
            VALUES (?, ?, ?, ?, ?, ?, ?::jsonb, ?, ?, ?, ?::jsonb[], ?, ?, ?)
            ON CONFLICT (object_id)
        """ + (fullMigrate? """
            DO NOTHING
        """: """
            DO UPDATE SET project_id = EXCLUDED.project_id, content = EXCLUDED.content, reply_to = EXCLUDED.reply_to, created_at = EXCLUDED.created_at, created_by = EXCLUDED.created_by, created_by_user = EXCLUDED.created_by_user, updated_at = EXCLUDED.updated_at, source = EXCLUDED.source, content_type = EXCLUDED.content_type, attachment = EXCLUDED.attachment, rich_text = EXCLUDED.rich_text, topic = EXCLUDED.topic, deleted = EXCLUDED.deleted;
        """);
        commentStream.addSink(JdbcSink.sink(
            commentSql,
            (statement, comment) -> {
                Connection connection = statement.getConnection();
                statement.setString(1, comment.getObjectId());
                statement.setLong(2, comment.getProjectId());
                statement.setString(3, comment.getContent());
                statement.setString(4, comment.getReplyTo());
                statement.setTimestamp(5, comment.getCreatedAt());
                statement.setString(6, comment.getCreatedBy());
                statement.setString(7, comment.getCreatedByUser());
                statement.setTimestamp(8, comment.getUpdatedAt());
                statement.setString(9, comment.getSource());
                statement.setString(10, comment.getContentType());
                statement.setArray(11, connection.createArrayOf("jsonb", comment.getAttachment()));
                statement.setString(12, comment.getRichText());
                statement.setString(13, comment.getTopic());
                statement.setBoolean(14, comment.isDeleted());
            },
            executionOptions,
            connectionOptions
        ));

        // 3. execute
        env.execute("activity-migrate");

        LOG.info("Finish to migrate activity!");
    }
}
