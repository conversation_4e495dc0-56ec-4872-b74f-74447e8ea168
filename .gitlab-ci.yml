default:
  image: harbor.9realms.co/private/ci-base:latest
  services:
    - name: harbor.9realms.co/public/docker:20.10.16-dind
      alias: docker
  tags:
    - sz-runner

stages:
  - pre-commit
  - style
  - test
  - build
  - pre-release
  - release

variables:
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: "/certs"
  US_S3_BUCKET: "bees360-flink-jar"
  US_S3_REGION: "us-east-1"
  SZ_S3_BUCKET: flink-qa
  SZ_S3_ENDPOINT: http://*************:9000

.maven-base-only-pull:
  cache:
    paths:
      - .m2/repository
    key: maven-cache
    policy: pull

.releases:
  stage: release
  only:
    refs:
      - master
      - /^release-.*$/i

check-java-format:
  stage: style
  only:
    refs:
      - merge_requests
    changes:
      - "**/*.java"
  cache: { }
  script:
    - ./check/style/check-java-format.sh

maven-compile:
  stage: test
  only:
    refs:
      - merge_requests
    changes:
      - "**/*.java"
  cache: { }
  script:
    - mvn compile -s .m2/settings.xml

build-jar:
  stage: build
  only:
    refs:
      - master
      - /^release-.*$/i
      - /^test-.*$/i
  cache:
    paths:
      - .m2/repository
    key: maven-cache
  artifacts:
    paths:
      - target-jars
  script:
    - mvn clean install -s .m2/settings.xml
    - mkdir target-jars
    - cp */*/bees360*.jar ./target-jars/

upload-to-release:
  stage: release
  only:
    refs:
      - /^release-.*$/i
      - /^test-.*$/i
  script:
    - export PROJECT_NAME=bees360-flink
    - export TAG_NAME=${CI_COMMIT_TAG:-${CI_COMMIT_REF_NAME}}  # Get tag name or branch name
    - cp ./target-jars/*.jar ./
    - |
      if [ -n "${CI_COMMIT_TAG}" ]; then
        export RELEASE_NAME="Release ${CI_COMMIT_TAG}"
        if [[ $CI_COMMIT_TAG =~ ^release(.*) ]]; then
          export RELEASE_VERSION="${BASH_REMATCH[1]}"
          export SUFFIX="-RELEASE"
        elif [[ $CI_COMMIT_TAG =~ ^test(.*) ]]; then
          export RELEASE_VERSION="${BASH_REMATCH[1]}"
          export SUFFIX="-SNAPSHOT"
        else
          echo "Invalid tag format: $tag"
          exit 1
        fi
      else
        export RELEASE_NAME="Build for ${CI_COMMIT_REF_NAME}"
        export RELEASE_VERSION=${CI_COMMIT_TAG}
        export SUFFIX="-SNAPSHOT"
      fi
    - |
      for file in *.jar; do
          # Get the base name (excluding the extension)
          base_name="${file%-1.0.0-SNAPSHOT.jar}"
          # Rename the file
          mv "$file" "${base_name}${RELEASE_VERSION}${SUFFIX}.jar"
      done
    - echo "Creating release ${RELEASE_NAME} with tag ${TAG_NAME}"
    - |
      description="**Release File List**"
      for jar_file in *.jar; do
        echo "Uploading ${jar_file} to release"
        description="${description}

      \`${jar_file}\`:"
        export AWS_ACCESS_KEY_ID=${SZ_S3_AWS_ACCESS_KEY_ID}
        export AWS_SECRET_ACCESS_KEY=${SZ_S3_AWS_SECRET_ACCESS_KEY}
        aws --endpoint-url ${SZ_S3_ENDPOINT} s3 cp "./${jar_file}" "s3://${SZ_S3_BUCKET}/flink-jar/${TAG_NAME}/${jar_file}"
        description="${description}

      SZ: <u>s3://${SZ_S3_BUCKET}/flink-jar/${TAG_NAME}/${jar_file}</u>"
        if [[ $CI_COMMIT_TAG =~ ^release(.*) ]]; then
          export AWS_ACCESS_KEY_ID=${US_AWS_ACCESS_KEY_ID}
          export AWS_SECRET_ACCESS_KEY=${US_AWS_SECRET_ACCESS_KEY}
          aws s3 cp "./${jar_file}" "s3://${US_S3_BUCKET}/flink-jar/${TAG_NAME}/${jar_file}" --region ${US_S3_REGION}
          description="${description}

      US: <u>s3://${US_S3_BUCKET}/flink-jar/${TAG_NAME}/${jar_file}</u>"
        fi
      done
      export DESCRIPTION=${description}
    - |
      curl --request POST --header "PRIVATE-TOKEN: ${GITLAB_CI}" \
        --form "name=${RELEASE_NAME}" \
        --form "tag_name=${TAG_NAME}" \
        --form "description=${DESCRIPTION}" \
        "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases"

pre-commit:
  stage: pre-commit
  only:
    refs:
      - merge_requests
  image: harbor.9realms.co/private/pre-commit:latest
  variables:
    GIT_CLONE_PATH: $CI_BUILDS_DIR/$CI_PROJECT_PATH
    PRE_COMMIT_HOME: ${CI_PROJECT_DIR}/.cache/pre-commit
  cache:
    key: pre-commit-cache
    paths:
      - ${PRE_COMMIT_HOME}
  before_script:
    - bash /opt/check_pre_commit_config.sh
  script:
    - pre-commit run --all-files --hook-stage commit

.sbom-check:
  variables:
    SBOM_PATH: "${CI_PROJECT_DIR}/target/bom.xml"
    SCANMETHOD: Source_code_scanning
    PROJECT_NAME: flink/flink
  except:
    - schedules
  image: harbor.9realms.co/private/ci-base
  script:
    - |
      if [[ $dependency_check == "NO" ]];then
        echo check skip
      else
        mvn org.cyclonedx:cyclonedx-maven-plugin:2.9.1:makeAggregateBom -s .m2/settings.xml
        python3 /opt/python-script/sbom_handle.py --step=upload_sbom
      fi
  after_script:
    - python3 /opt/python-script/sbom_handle.py --step=upload_nocobase

.check_file_change:
  before_script:
    - git fetch origin ${CI_DEFAULT_BRANCH}
    - change_files=`git diff --name-only $CI_COMMIT_SHA remotes/origin/${CI_DEFAULT_BRANCH}`
    - echo "change_files $change_files"
    - |
      if echo "$change_files" | egrep ${FILE_CHANGE}$;then
        echo There are dependency updates that need to be checked
      else
        echo Dependency is not updated, skip
        export dependency_check='NO'
      fi

sbom-check-mr:
  stage: test
  extends: .sbom-check
  variables:
    PAREN_PROJECT_NAME: flink/flink
    PROJECT_VERSION: ${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME}
    TIGGER_TYPE: "mr"
  only:
    refs:
      - merge_requests
    changes:
      - "**/pom.xml"

sbom-check-tag:
  stage: pre-release
  extends:
    - .check_file_change
    - .sbom-check
  variables:
    PAREN_PROJECT_NAME: flink/flink
    PROJECT_VERSION: ${CI_COMMIT_TAG}
    TIGGER_TYPE: "tag"
    FILE_CHANGE: "pom.xml"
  only:
    refs:
      - /^release-.*$/i
      - /^test-.*$/i
    changes:
      - "**/pom.xml"

sbom-check:
  stage: release
  extends: .sbom-check
  variables:
    PAREN_PROJECT_NAME: flink
    PROJECT_VERSION: main
    TIGGER_TYPE: "merge_event"
  before_script:
    - |
      MERGE_INFO=$(curl --header "PRIVATE-TOKEN: ${GITLAB_READ_TOKEN}" \
      "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/repository/commits/${CI_COMMIT_SHA}/merge_requests")
      SOURCE_BRANCH=$(echo $MERGE_INFO | jq -r '.[0].source_branch')
      echo "Source branch was: $SOURCE_BRANCH"
      export SUB_PROJECT_VERSION=$SOURCE_BRANCH
  only:
    refs:
      - main
    changes:
      - "**/pom.xml"

.code-owner:
  cache: {}
  except:
    - schedules
  image: harbor.9realms.co/ops/ops-ci:latest
  only:
    refs:
      - merge_requests
  script:
    - git fetch origin ${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}
    - python3 /opt/check_code_owner.py

check-commit-emails&&show-code-owner:
  stage: pre-commit
  variables:
    CODE_OWNERS_STEP: SHOW_CODE_OWNERS
  before_script:
    - python3 /opt/check_email.py
  extends:
    - .code-owner

check-code-owner:
  stage: release
  extends:
    - .code-owner
