package com.bees360.flink;

import com.alibaba.fastjson.JSON;
import com.bees360.flink.entity.ProjectBundle;
import com.bees360.flink.util.EnvUtils;
import com.bees360.flink.util.FileUtils;
import com.bees360.flink.util.Plugins;
import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.test.util.MiniClusterWithClientResource;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.junit.ClassRule;
import org.junit.Test;

public class TestProjectBundle {

    @ClassRule
    public static MiniClusterWithClientResource flinkCluster =
            new MiniClusterWithClientResource(
                    new MiniClusterResourceConfiguration.Builder()
                            .setNumberSlotsPerTaskManager(2)
                            .setNumberTaskManagers(1)
                            .build());

    public static final String DDL_SQL = "project_bundle_ddl.sql";
    public static final String QUERY_SQL = "project_bundle_sql_query.sql";

    @Test
    public void testSyncProjectBundle() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        StreamTableEnvironment streamTableEnv = StreamTableEnvironment.create(env);

        var properties = EnvUtils.getProperties(new String[] {});
        properties.resetKafkaConfig(ProjectBundleSyncJob.JOB_NAME);
        FileUtils.getSql(
                        ProjectBundleSyncJob.class,
                        DDL_SQL,
                        properties,
                        Plugins.loadTopicReplaceHandler(new String[] {}))
                .forEach(streamTableEnv::executeSql);

        Table resultTable =
                streamTableEnv.sqlQuery(
                        FileUtils.getSqlFile(ProjectBundleSyncJob.class, QUERY_SQL));

        DataStream<Row> input = streamTableEnv.toChangelogStream(resultTable);
        input.filter(e -> RowKind.INSERT.equals(e.getKind()))
                .map(ProjectBundle::fromRow)
                .map(JSON::toJSONString)
                .print();
        // env.execute();
    }
}
