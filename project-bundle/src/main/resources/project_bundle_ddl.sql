CREATE TABLE bundle (
    id           BIGINT,
    contract_id  BIGINT,
    address_id   BIGINT,
    service_type INT,
    policy_no    STRING,
    inspection_no STRING,
    state        STRING,
    status       STRING,
    metadata     STRING,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'kafka',
    'properties.bootstrap.servers' = '$kafka-url',
    'properties.group.id' = '$kafka-group',
    'topic' = '$kafka-topic-prefix.public.bundle',
    'scan.startup.mode' = 'earliest-offset',
    'value.debezium-json.schema-include' = 'true',
    'value.debezium-json.timestamp-format.standard' = 'ISO-8601',
    'value.format' = 'debezium-json'
);

CREATE TABLE bundle_contact (
    id         BIGINT,
    bundle_id  BIGINT,
    role       STRING,
    is_primary BOOLEAN,
    full_name  STRING,
    email      STRING,
    phone      STRING,
    deleted    BOOLEAN,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'kafka',
    'properties.bootstrap.servers' = '$kafka-url',
    'properties.group.id' = '$kafka-group',
    'topic' = '$kafka-topic-prefix.public.bundle_contact',
    'scan.startup.mode' = 'earliest-offset',
    'value.debezium-json.schema-include' = 'true',
    'value.debezium-json.timestamp-format.standard' = 'ISO-8601',
    'value.format' = 'debezium-json'
);

CREATE TABLE project_group (
    id         BIGINT,
    group_key  STRING,
    group_type STRING,
    project_id BIGINT,
    is_deleted BOOLEAN,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'kafka',
    'properties.bootstrap.servers' = '$kafka-url',
    'properties.group.id' = '$kafka-group',
    'topic' = '$kafka-topic-prefix.public.project_group',
    'scan.startup.mode' = 'earliest-offset',
    'value.debezium-json.schema-include' = 'true',
    'value.debezium-json.timestamp-format.standard' = 'ISO-8601',
    'value.format' = 'debezium-json'
);

CREATE TABLE address (
    id             BIGINT,
    lat            DOUBLE,
    lng            DOUBLE,
    address        STRING,
    street_number  STRING,
    route          STRING,
    city           STRING,
    county         STRING,
    state          STRING,
    country        STRING,
    zip            STRING,
    street_address STRING,
    created_at     TIMESTAMP_LTZ,
    updated_at     TIMESTAMP_LTZ,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'kafka',
    'properties.bootstrap.servers' = '$kafka-url',
    'properties.group.id' = '$kafka-group',
    'topic' = '$kafka-topic-prefix.public.address',
    'scan.startup.mode' = 'earliest-offset',
    'value.debezium-json.schema-include' = 'true',
    'value.debezium-json.timestamp-format.standard' = 'ISO-8601',
    'value.format' = 'debezium-json'
);

CREATE TABLE contract (
    id           BIGINT,
    insured_by   BIGINT,
    processed_by BIGINT,
    created_at   TIMESTAMP_LTZ,
    updated_at   TIMESTAMP_LTZ,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'kafka',
    'properties.bootstrap.servers' = '$kafka-url',
    'properties.group.id' = '$kafka-group',
    'topic' = '$kafka-topic-prefix.public.contract',
    'scan.startup.mode' = 'earliest-offset',
    'value.debezium-json.schema-include' = 'true',
    'value.debezium-json.timestamp-format.standard' = 'ISO-8601',
    'value.format' = 'debezium-json'
);

CREATE TABLE customer (
    id         BIGINT,
    name       STRING,
    key        STRING,
    website    STRING,
    logo       STRING,
    created_at TIMESTAMP_LTZ,
    updated_at TIMESTAMP_LTZ,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'kafka',
    'properties.bootstrap.servers' = '$kafka-url',
    'properties.group.id' = '$kafka-group',
    'topic' = '$kafka-topic-prefix.public.customer',
    'scan.startup.mode' = 'earliest-offset',
    'value.debezium-json.schema-include' = 'true',
    'value.debezium-json.timestamp-format.standard' = 'ISO-8601',
    'value.format' = 'debezium-json'
);