SELECT
  pg.project_id AS id,
  (b.id, b.service_type, b.policy_no, b.inspection_no, b.state, b.status, b.metadata) AS bundle,
  (a.id, a.lat, a.lng, a.address, a.street_number, a.route, a.city, a.county, a.state, a.country, a.zip, a.street_address) AS address,
  (c.insured_by, c.insured_name, c.insured_key, c.insured_logo) AS insured_by,
  (c.processed_by, c.processed_name, c.processed_key, c.processed_logo) AS processed_by,
  bc.contacts AS bundle_contact
FROM project_group pg
JOIN bundle b ON pg.group_key = CAST(b.id AS VARCHAR) AND pg.group_type = 'BUNDLE_PROJECT'
LEFT JOIN address a ON b.address_id = a.id
LEFT JOIN (
    SELECT
      ct.id,
      ct.insured_by,
      c1.name AS insured_name,
      c1.key AS insured_key,
      c1.logo AS insured_logo,
      ct.processed_by,
      c2.name AS processed_name,
      c2.key AS processed_key,
      c2.logo AS processed_logo
    FROM contract ct
    LEFT JOIN customer c1 ON c1.id = ct.insured_by
    LEFT JOIN customer c2 ON c2.id = ct.processed_by
) c ON b.contract_id = c.id
LEFT JOIN (
    SELECT
      bundle_id,
      collect(
        ROW(
          id,
          role,
          is_primary,
          full_name,
          email,
          phone,
          deleted
        )
      ) AS contacts
    FROM bundle_contact
    WHERE deleted IS NULL OR deleted = FALSE
    GROUP BY bundle_id
) bc ON b.id = bc.bundle_id
WHERE pg.is_deleted = FALSE;