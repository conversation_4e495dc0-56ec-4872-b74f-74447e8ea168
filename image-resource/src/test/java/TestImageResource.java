import com.alibaba.fastjson.JSON;
import com.bees360.flink.ImageResourceSyncJob;
import com.bees360.flink.entity.ImageResource;
import com.bees360.flink.util.EnvUtils;
import com.bees360.flink.util.FileUtils;
import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.test.util.MiniClusterWithClientResource;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.junit.ClassRule;
import org.junit.Test;

public class TestImageResource {

    @ClassRule
    public static MiniClusterWithClientResource flinkCluster =
            new MiniClusterWithClientResource(
                    new MiniClusterResourceConfiguration.Builder()
                            .setNumberSlotsPerTaskManager(2)
                            .setNumberTaskManagers(1)
                            .build());

    public static final String DDL_SQL = "image_resource_sql_ddl.sql";
    public static final String QUERY_SQL = "image_resource_sql_query.sql";

    @Test
    public void testSyncImageResource() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // configure your test environment
        env.setParallelism(1);
        StreamTableEnvironment streamTableEnv = StreamTableEnvironment.create(env);

        var properties = EnvUtils.getImageProperties(new String[]{});
        properties.resetKafkaConfig(ImageResourceSyncJob.JOB_NAME);
        FileUtils.getSql(ImageResourceSyncJob.class, DDL_SQL, properties)
                .forEach(streamTableEnv::executeSql);
        Table resultTable =
                streamTableEnv.sqlQuery(
                        FileUtils.getSqlFile(ImageResourceSyncJob.class, QUERY_SQL));

        // interpret the updating Table as a changelog DataStream
        DataStream<Row> input = streamTableEnv.toChangelogStream(resultTable);
        input.map(
                        e -> {
                            System.out.println(e);
                            return e;
                        })
                .filter(e -> RowKind.INSERT.equals(e.getKind()))
                .map(ImageResource::getImageResource)
                .filter(x -> x.getImageId() != null)
                .keyBy(ImageResource::getImageId)
                .map(JSON::toJSONString);
        //        env.execute();
    }
}
