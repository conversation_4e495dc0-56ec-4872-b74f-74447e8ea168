<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.grpc</groupId>
  <artifactId>grpc-protobuf</artifactId>
  <version>1.68.2</version>
  <name>io.grpc:grpc-protobuf</name>
  <description>gRPC: Protobuf</description>
  <url>https://github.com/grpc/grpc-java</url>
  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>https://opensource.org/licenses/Apache-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>grpc.io</id>
      <name>gRPC Contributors</name>
      <email><EMAIL></email>
      <url>https://grpc.io/</url>
      <organization>gRPC Authors</organization>
      <organizationUrl>https://www.google.com</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/grpc/grpc-java.git</connection>
    <developerConnection>scm:git:**************:grpc/grpc-java.git</developerConnection>
    <url>https://github.com/grpc/grpc-java</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-api</artifactId>
      <version>1.68.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.25.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-common-protos</artifactId>
      <version>2.41.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>33.2.1-android</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf-lite</artifactId>
      <version>1.68.2</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-javalite</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
</project>
