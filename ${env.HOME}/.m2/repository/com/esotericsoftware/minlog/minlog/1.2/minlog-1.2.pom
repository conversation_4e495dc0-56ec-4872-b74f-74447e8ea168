<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
		http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.esotericsoftware.minlog</groupId>
	<artifactId>minlog</artifactId>
	<version>1.2</version>
	<packaging>jar</packaging>
	<name>MinLog</name>
	<description>Minimal overhead Java logging</description>
	<url>http://code.google.com/p/minlog/</url>

	<licenses>
		<license>
			<name>New BSD License</name>
			<url>http://www.opensource.org/licenses/bsd-license.php</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<scm>
		<url>http://minlog.googlecode.com/svn/</url>
		<connection>scm:svn:http://minlog.googlecode.com/svn/</connection>
	</scm>

	<developers>
		<developer>
			<id>nathan.sweet</id>
			<name>Nathan Sweet</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<build>
		<!-- Set nonstandard source dir -->
		<sourceDirectory>src</sourceDirectory>

		<plugins>
			<!-- Disable resources (project has none) -->
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.5</version>
				<executions>
					<execution>
						<id>default-resources</id>
						<phase>none</phase>
					</execution>
					<execution>
						<id>default-testResources</id>
						<phase>none</phase>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
