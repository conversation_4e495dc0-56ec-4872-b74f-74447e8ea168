<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
		http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.sonatype.oss</groupId>
		<artifactId>oss-parent</artifactId>
		<version>7</version>
	</parent>
	<groupId>com.esotericsoftware.kryo</groupId>
	<artifactId>kryo</artifactId>
	<version>2.21</version>
	<packaging>bundle</packaging>
	<name>Kryo</name>
	<description>Fast, efficient Java serialization</description>
	<url>http://code.google.com/p/kryo/</url>

	<licenses>
		<license>
			<name>New BSD License</name>
			<url>http://www.opensource.org/licenses/bsd-license.php</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<scm>
		<url>http://kryo.googlecode.com/svn/</url>
		<connection>scm:svn:http://kryo.googlecode.com/svn/</connection>
	</scm>

	<developers>
		<developer>
			<id>nathan.sweet</id>
			<name>Nathan Sweet</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.esotericsoftware.reflectasm</groupId>
			<artifactId>reflectasm</artifactId>
			<version>1.07</version>
			<classifier>shaded</classifier>
		</dependency>
		<dependency>
			<groupId>com.esotericsoftware.minlog</groupId>
			<artifactId>minlog</artifactId>
			<version>1.2</version>
		</dependency>
		<dependency>
			<groupId>org.objenesis</groupId>
			<artifactId>objenesis</artifactId>
			<version>1.2</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.8.2</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<!-- Set nonstandard source and test source dirs -->
		<sourceDirectory>src</sourceDirectory>
		<testSourceDirectory>test</testSourceDirectory>

		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<inherited>true</inherited>
					<configuration>
						<source>1.5</source>
						<target>1.5</target>
						<encoding>utf-8</encoding>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<!-- Disable resources (project has none) -->
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.5</version>
				<executions>
					<execution>
						<id>default-resources</id>
						<phase>none</phase>
					</execution>
					<execution>
						<id>default-testResources</id>
						<phase>none</phase>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<excludes>
						<exclude>**/.svn/*</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<version>1.7</version>
				<configuration>
					<shadedArtifactAttached>true</shadedArtifactAttached>
					<shadedClassifierName>shaded</shadedClassifierName>
					<artifactSet>
						<includes>
							<include>com.esotericsoftware.reflectasm:reflectasm:shaded</include>
							<include>com.esotericsoftware.minlog:minlog</include>
							<include>org.objenesis:objenesis</include>
						</includes>
					</artifactSet>
					<relocations>
						<relocation>
							<pattern>org.objenesis</pattern>
							<shadedPattern>com.esotericsoftware.shaded.org.objenesis</shadedPattern>
						</relocation>
					</relocations>
				</configuration>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<extensions>true</extensions>
				<configuration>
					<instructions>
						<Import-Package><![CDATA[
						COM.jrockit*;resolution:=optional,
						COM.newmonics*;resolution:=optional,
						jrockit*;resolution:=optional,
						sun.reflect;resolution:=optional,
						*
						]]>
						</Import-Package>
						<Export-Package>com.esotericsoftware*, org.objenesis*</Export-Package>
						<Embed-Dependency>*;scope=compile|runtime</Embed-Dependency>
					</instructions>
				</configuration>
                       </plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>requireSnapshot</id>
			<!-- This profile is activated by the "-P requireSnapshot" switch to
				be able to deploy SNAPSHOTs only (which is not allowed for releases) -->
			<build>
				<plugins>
					<!-- Jenkins should only build -SNAPSHOTs -->
					<plugin>
						<artifactId>maven-enforcer-plugin</artifactId>
						<executions>
							<execution>
								<goals>
									<goal>enforce</goal>
								</goals>
								<configuration>
									<rules>
										<evaluateBeanshell>
											<condition>"${project.version}".endsWith("-SNAPSHOT")</condition>
											<message>Jenkins should only build -SNAPSHOT versions</message>
										</evaluateBeanshell>
									</rules>
									<fail>true</fail>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<repositories>
		<repository>
			<id>sonatype-releases</id>
			<name>sonatype releases repo</name>
			<url>https://oss.sonatype.org/content/repositories/releases</url>
		</repository>
	</repositories>
</project>
