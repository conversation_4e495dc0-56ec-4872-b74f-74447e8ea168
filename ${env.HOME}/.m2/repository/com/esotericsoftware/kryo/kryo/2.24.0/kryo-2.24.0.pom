<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>oss-parent</artifactId>
    <groupId>org.sonatype.oss</groupId>
    <version>7</version>
    <relativePath>../pom.xml/pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.esotericsoftware.kryo</groupId>
  <artifactId>kryo</artifactId>
  <packaging>bundle</packaging>
  <name>Kryo</name>
  <version>2.24.0</version>
  <description>Fast, efficient Java serialization</description>
  <url>https://github.com/EsotericSoftware/kryo</url>
  <developers>
    <developer>
      <id>nathan.sweet</id>
      <name><PERSON></name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <licenses>
    <license>
      <name>New BSD License</name>
      <url>http://www.opensource.org/licenses/bsd-license.php</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:**************:EsotericSoftware/kryo.git</connection>
    <developerConnection>scm:git:**************:EsotericSoftware/kryo.git</developerConnection>
    <url>https://github.com/EsotericSoftware/kryo</url>
  </scm>
  <build>
    <sourceDirectory>src</sourceDirectory>
    <testSourceDirectory>test</testSourceDirectory>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <inherited>true</inherited>
          <configuration>
            <source>1.5</source>
            <target>1.5</target>
            <encoding>utf-8</encoding>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>2.5</version>
        <executions>
          <execution>
            <id>default-resources</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>default-testResources</id>
            <phase>none</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <excludes>
            <exclude>**/.svn/*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>2.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <minimizeJar>true</minimizeJar>
          <shadedArtifactAttached>false</shadedArtifactAttached>
          <artifactSet>
            <includes>
              <include>com.esotericsoftware.reflectasm:reflectasm:shaded</include>
            </includes>
          </artifactSet>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>2.4.0</version>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <Import-Package>COM.jrockit*;resolution:=optional,
						COM.newmonics*;resolution:=optional,
						jrockit*;resolution:=optional,
						sun.reflect;resolution:=optional,
						sun.misc;resolution:=optional,
						sun.nio.ch;resolution:=optional,
						*</Import-Package>
            <Export-Package>com.esotericsoftware.kryo*,com.esotericsoftware.reflectasm*</Export-Package>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.4</version>
        <executions>
          <execution>
            <id>dist-assembly</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <descriptor>etc/assembly-all.xml</descriptor>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>requireSnapshot</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <evaluateBeanshell>
                      <condition>"${project.version}".endsWith("-SNAPSHOT")</condition>
                      <message>Jenkins should only build -SNAPSHOT versions</message>
                    </evaluateBeanshell>
                  </rules>
                  <fail>true</fail>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <repositories>
    <repository>
      <id>sonatype-releases</id>
      <name>sonatype releases repo</name>
      <url>https://oss.sonatype.org/content/repositories/releases</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>com.esotericsoftware.minlog</groupId>
      <artifactId>minlog</artifactId>
      <version>1.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.objenesis</groupId>
      <artifactId>objenesis</artifactId>
      <version>2.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.8.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
</project>

