<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.github.docker-java</groupId>
		<artifactId>docker-java-parent</artifactId>
		<version>3.3.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>docker-java-transport</artifactId>
	<packaging>jar</packaging>

	<name>docker-java-transport</name>
	<url>https://github.com/docker-java/docker-java</url>
	<description>Java API Client for Docker</description>

	<properties>
		<automatic.module.name>com.github.dockerjava.transport</automatic.module.name>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>annotations</artifactId>
			<version>3.0.1u2</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.immutables</groupId>
			<artifactId>value</artifactId>
			<version>2.8.2</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna</artifactId>
			<version>5.12.1</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<extensions>true</extensions>
				<configuration>
					<instructions>
						<Export-Package>com.github.dockerjava.transport.*</Export-Package>
					</instructions>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
