<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fasterxml.jackson.module</groupId>
    <artifactId>jackson-modules-java8</artifactId>
    <version>2.15.2</version>
  </parent>
  <groupId>com.fasterxml.jackson.datatype</groupId>
  <artifactId>jackson-datatype-jdk8</artifactId>
  <name>Jackson datatype: jdk8</name>
  <packaging>bundle</packaging>
  <description>Add-on module for Jackson (http://jackson.codehaus.org) to support
JDK 8 data types.
  </description>

  <properties>
    <!-- explicitly target JDK 8 -->
    <javac.src.version>1.8</javac.src.version>
    <javac.target.version>1.8</javac.target.version>

    <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/datatype/jdk8</packageVersion.dir>
    <packageVersion.package>${project.groupId}.jdk8</packageVersion.package>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <version>${version.plugin.surefire}</version>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>com/fasterxml/jackson/failing/*.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
      <!--  28-Mar-2019, tatu: Add rudimentary JDK9+ module info. To build with JDK 8
             will have to use `moduleInfoFile` as anything else requires JDK 9+
        -->
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
