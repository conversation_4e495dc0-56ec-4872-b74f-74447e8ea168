<?xml version='1.0' encoding='UTF-8'?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.twitter</groupId>
    <artifactId>chill-java</artifactId>
    <packaging>jar</packaging>
    <description>chill-java</description>
    <version>0.7.6</version>
    <name>chill-java</name>
    <organization>
        <name>com.twitter</name>
    </organization>
    <url>https://github.com/twitter/chill</url>
    <licenses>
        <license>
            <name>Apache 2</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
            <comments>A business-friendly OSS license</comments>
        </license>
    </licenses>
    <scm>
        <url>**************:twitter/chill.git</url>
        <connection>scm:git:**************:twitter/chill.git</connection>
    </scm>
    <developers>
        <developer>
            <id>oscar</id>
            <name>Oscar Boykin</name>
            <url>http://twitter.com/posco</url>
        </developer>
        <developer>
            <id>sritchie</id>
            <name>Sam Ritchie</name>
            <url>http://twitter.com/sritchie</url>
        </developer>
    </developers>
    <dependencies>
        <dependency>
            <groupId>org.scalacheck</groupId>
            <artifactId>scalacheck_2.10</artifactId>
            <version>1.11.6</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.10</artifactId>
            <version>3.0.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.esotericsoftware.kryo</groupId>
            <artifactId>kryo</artifactId>
            <version>2.21</version>
        </dependency>
    </dependencies>
</project>