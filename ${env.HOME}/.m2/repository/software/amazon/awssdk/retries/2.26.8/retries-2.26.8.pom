<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License").
  ~ You may not use this file except in compliance with the License.
  ~ A copy of the License is located at
  ~
  ~  http://aws.amazon.com/apache2.0
  ~
  ~ or in the "license" file accompanying this file. This file is distributed
  ~ on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
  ~ express or implied. See the License for the specific language governing
  ~ permissions and limitations under the License.
  ~ 
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>core</artifactId>
        <groupId>software.amazon.awssdk</groupId>
        <version>2.26.8</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>retries</artifactId>
    <name>AWS Java SDK :: Retries</name>
    <build>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <configuration>
            <archive>
              <manifestEntries>
                <Automatic-Module-Name>software.amazon.awssdk.retries</Automatic-Module-Name>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
      </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>retries-spi</artifactId>
            <version>${awsjavasdk.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>annotations</artifactId>
            <version>${awsjavasdk.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>utils</artifactId>
            <version>${awsjavasdk.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit5.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
