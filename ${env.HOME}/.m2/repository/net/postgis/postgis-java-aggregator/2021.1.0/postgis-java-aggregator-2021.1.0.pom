<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>net.postgis</groupId>
    <artifactId>postgis-java-aggregator</artifactId>
    <version>2021.1.0</version>
    <packaging>pom</packaging>

    <name>PostGIS Java Project</name>
    <description>Aggregates java projects associated with the PostGIS project.</description>
    <url>http://postgis.net</url>
    <licenses>
        <license>
            <name>GNU Lesser General Public License</name>
            <url>http://www.gnu.org/licenses/lgpl-2.1.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>PostGIS Development Team</name>
            <url>http://postgis.net</url>
        </developer>
        <developer>
            <name><PERSON></name>
            <email>phillip.w.g.ross at gmail.com</email>
            <roles>
                <role>Maintainer</role>
            </roles>
        </developer>
    </developers>
    <contributors>
        <contributor>
            <name>Hakan Tandogan</name>
            <email><EMAIL></email>
            <url>http://www.gurkensalat.com/</url>
            <roles>
                <role>Maven Packager</role>
            </roles>
        </contributor>
        <contributor>
            <name>Baris Ergun</name>
            <email><EMAIL></email>
            <url>http://www.barisergun.com/</url>
            <roles>
                <role>Maven Packager</role>
            </roles>
        </contributor>
    </contributors>

    <mailingLists>
        <mailingList>
            <name>User List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
            <archive>http://lists.osgeo.org/pipermail/postgis-users/</archive>
        </mailingList>
        <mailingList>
            <name>Developer List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
            <archive>http://lists.osgeo.org/pipermail/postgis-devel/</archive>
        </mailingList>
    </mailingLists>

    <modules>
        <module>postgis-jdbc</module>
        <module>postgis-jdbc-geometry</module>
        <module>postgis-jdbc-java2d</module>
        <module>postgis-jdbc-jts</module>
        <module>tools</module>
    </modules>

    <scm>
        <url>https://github.com/postgis/postgis-java</url>
        <connection>scm:git:git://github.com/postgis/postgis-java.git</connection>
        <developerConnection>scm:git:**************:postgis/postgis-java.git</developerConnection>
        <tag>postgis-java-aggregator-2021.1.0</tag>
    </scm>
    <issueManagement>
        <system>GitHub Issues</system>
        <url>https://github.com/postgis/postgis-java/issues</url>
    </issueManagement>
    <distributionManagement>
        <site>
            <id>local-staging-site</id>
            <url>http://local-staging/</url>
        </site>
    </distributionManagement>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.min.version>8</java.min.version>
        <maven.min.version>3.6</maven.min.version>
        <maven.test.skip>false</maven.test.skip>
        <maven.integration.test.skip>false</maven.integration.test.skip>
        <failsafe.forkCount>1</failsafe.forkCount>
        <failsafe.useSystemClassLoader>true</failsafe.useSystemClassLoader>
        <surefire.forkCount>1</surefire.forkCount>
        <surefire.useSystemClassLoader>true</surefire.useSystemClassLoader>
        <javadoc.path>${java.home}/bin/javadoc</javadoc.path>
        <checkstyle.skip>true</checkstyle.skip>
        <cpd.skip>true</cpd.skip>
        <jxr.skip>false</jxr.skip>
        <pmd.skip>true</pmd.skip>
        <spotbugs.skip>true</spotbugs.skip>
        <test.container.image>postgis/postgis:13-3.1-alpine</test.container.image>
        <test.db.username>postgis1</test.db.username>
        <test.db.password>postgis1</test.db.password>
        <test.db.name>postgis1</test.db.name>
        <test.db.port>5432</test.db.port>
        <!-- Plugin versioning -->
        <build-helper-maven-plugin.version>3.2.0</build-helper-maven-plugin.version>
        <directory-maven-plugin.version>0.3.1</directory-maven-plugin.version>
        <download-maven-plugin.version>1.6.6</download-maven-plugin.version>
        <exec-maven-plugin.version>3.0.0</exec-maven-plugin.version>
        <git-commit-id-plugin.version>4.0.5</git-commit-id-plugin.version>
        <jacoco-maven-plugin.version>0.8.7</jacoco-maven-plugin.version>
        <maven-antrun-plugin.version>3.0.0</maven-antrun-plugin.version>
        <maven-archetype-plugin.version>3.2.0</maven-archetype-plugin.version>
        <maven-assembly-plugin.version>3.3.0</maven-assembly-plugin.version>
        <maven-checkstyle-plugin.version>3.1.2</maven-checkstyle-plugin.version>
        <maven-clean-plugin.version>3.1.0</maven-clean-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-dependency-plugin.version>3.2.0</maven-dependency-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-ear-plugin.version>3.2.0</maven-ear-plugin.version>
        <maven-ejb-plugin.version>3.0.1</maven-ejb-plugin.version>
        <maven-enforcer-plugin.version>3.0.0</maven-enforcer-plugin.version>
        <maven-failsafe-plugin.version>2.22.2</maven-failsafe-plugin.version>
        <maven-gpg-plugin.version>3.0.1</maven-gpg-plugin.version>
        <maven-install-plugin.version>2.5.2</maven-install-plugin.version>
        <maven-jar-plugin.version>3.2.0</maven-jar-plugin.version>
        <maven-jarsigner-plugin.version>3.0.0</maven-jarsigner-plugin.version>
        <maven-javadoc-plugin.version>3.3.0</maven-javadoc-plugin.version>
        <maven-jxr-plugin.version>3.1.1</maven-jxr-plugin.version>
        <maven-pmd-plugin.version>3.14.0</maven-pmd-plugin.version>
        <maven-project-info-reports-plugin.version>3.1.2</maven-project-info-reports-plugin.version>
        <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
        <maven-resources-plugin.version>3.2.0</maven-resources-plugin.version>
        <maven-shade-plugin.version>3.2.4</maven-shade-plugin.version>
        <maven-site-plugin.version>3.9.1</maven-site-plugin.version>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
        <maven-surefire-report-plugin.version>2.22.2</maven-surefire-report-plugin.version>
        <maven-war-plugin.version>3.3.1</maven-war-plugin.version>
        <spotbugs-maven-plugin.version>4.3.0</spotbugs-maven-plugin.version>
        <versions-maven-plugin.version>2.8.1</versions-maven-plugin.version>
        <!-- Dependency versions -->
        <dependency.checkstyle.version>8.45</dependency.checkstyle.version>
        <dependency.jts-version.version>1.18.1</dependency.jts-version.version>
        <dependency.logback.version>1.2.5</dependency.logback.version>
        <dependency.pmd.version>6.37.0</dependency.pmd.version>
        <dependency.postgresql-jdbc.version>42.2.23</dependency.postgresql-jdbc.version>
        <dependency.slf4j.version>1.7.32</dependency.slf4j.version>
        <dependency.spatial4j.version>0.8</dependency.spatial4j.version>
        <dependency.spotbugs.version>4.3.0</dependency.spotbugs.version>
        <dependency.testcontainers.version>1.16.0</dependency.testcontainers.version>
        <dependency.testng.version>6.14.3</dependency.testng.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${dependency.testcontainers.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${dependency.logback.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${dependency.logback.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${dependency.slf4j.version}</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/resources-filtered</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/test/resources-filtered</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.github.spotbugs</groupId>
                    <artifactId>spotbugs-maven-plugin</artifactId>
                    <version>${spotbugs-maven-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.github.spotbugs</groupId>
                            <artifactId>spotbugs</artifactId>
                            <version>${dependency.spotbugs.version}</version>
                        </dependency>
                    </dependencies>
                    <executions>
                        <execution>
                            <id>spotbugs-check</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <skip>${spotbugs.skip}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.googlecode.maven-download-plugin</groupId>
                    <artifactId>download-maven-plugin</artifactId>
                    <version>${download-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-archetype-plugin</artifactId>
                    <version>${maven-archetype-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${maven-assembly-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven-checkstyle-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${dependency.checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <executions>
                        <execution>
                            <id>checkstyle</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <configLocation>checkstyle.xml</configLocation>
                        <consoleOutput>true</consoleOutput>
                        <skip>${checkstyle.skip}</skip>
                        <violationSeverity>warning</violationSeverity>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${maven-clean-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.min.version}</source>
                        <target>${java.min.version}</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven-dependency-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ear-plugin</artifactId>
                    <version>${maven-ear-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${maven-enforcer-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>enforce-maven</id>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <rules>
                            <requireMavenVersion>
                                <version>[${maven.min.version},)</version>
                            </requireMavenVersion>
                        </rules>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>integration-tests</id>
                            <goals>
                                <goal>integration-test</goal>
                                <goal>verify</goal>
                            </goals>
                            <configuration>
                                <forkCount>${failsafe.forkCount}</forkCount>
                                <skip>${maven.integration.test.skip}</skip>
                                <suiteXmlFiles>
                                    <suiteXmlFile>${project.build.testOutputDirectory}/testng-it.xml</suiteXmlFile>
                                </suiteXmlFiles>
                                <useSystemClassLoader>${failsafe.useSystemClassLoader}</useSystemClassLoader>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>${maven-gpg-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven-install-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jarsigner-plugin</artifactId>
                    <version>${maven-jarsigner-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                    <configuration>
                        <javadocExecutable>${javadoc.path}</javadocExecutable>
                        <source>${java.min.version}</source>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jxr-plugin</artifactId>
                    <version>${maven-jxr-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-pmd-plugin</artifactId>
                    <version>${maven-pmd-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>net.sourceforge.pmd</groupId>
                            <artifactId>pmd-core</artifactId>
                            <version>${dependency.pmd.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>net.sourceforge.pmd</groupId>
                            <artifactId>pmd-java</artifactId>
                            <version>${dependency.pmd.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>net.sourceforge.pmd</groupId>
                            <artifactId>pmd-javascript</artifactId>
                            <version>${dependency.pmd.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>net.sourceforge.pmd</groupId>
                            <artifactId>pmd-jsp</artifactId>
                            <version>${dependency.pmd.version}</version>
                        </dependency>
                    </dependencies>
                    <executions>
                        <execution>
                            <id>pmd-check</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                            <configuration>
                                <skip>${pmd.skip}</skip>
                            </configuration>
                        </execution>
                        <execution>
                            <id>cpd-check</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>cpd-check</goal>
                            </goals>
                            <configuration>
                                <skip>${cpd.skip}</skip>
                            </configuration>
                        </execution>
                    </executions>
                    <configuration>
                        <printFailingErrors>true</printFailingErrors>
                        <rulesets>
                            <!--suppress UnresolvedMavenProperty -->
                            <ruleset>file:///${multi.module.root}/pmd.xml</ruleset>
                        </rulesets>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-project-info-reports-plugin</artifactId>
                    <version>${maven-project-info-reports-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>${maven-release-plugin.version}</version>
                    <configuration>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <localCheckout>true</localCheckout>
                        <pushChanges>false</pushChanges>
                        <releaseProfiles>release-sign-artifacts,sonatype-deployment</releaseProfiles>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin.version}</version>
                    <configuration>
                        <skipDeploy>true</skipDeploy>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <forkCount>${surefire.forkCount}</forkCount>
                        <skip>${maven.test.skip}</skip>
                        <suiteXmlFiles>
                            <suiteXmlFile>${project.build.testOutputDirectory}/testng.xml</suiteXmlFile>
                        </suiteXmlFiles>
                        <useSystemClassLoader>${surefire.useSystemClassLoader}</useSystemClassLoader>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>${maven-surefire-report-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>${maven-war-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${build-helper-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>${exec-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${versions-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>check-parent-versions-update</id>
                            <goals>
                                <goal>display-parent-updates</goal>
                            </goals>
                            <phase>validate</phase>
                        </execution>
                    </executions>
                    <configuration>
                        <!--suppress UnresolvedMavenProperty -->
                        <rulesUri>file:///${multi.module.root}/maven-version-rules.xml</rulesUri>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.commonjava.maven.plugins</groupId>
                    <artifactId>directory-maven-plugin</artifactId>
                    <version>${directory-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>multi-module-root-directory-build</id>
                            <goals>
                                <goal>directory-of</goal>
                            </goals>
                            <phase>validate</phase>
                            <configuration>
                                <property>multi.module.root</property>
                                <project>
                                    <groupId>net.postgis</groupId>
                                    <artifactId>postgis-java-aggregator</artifactId>
                                </project>
                            </configuration>
                        </execution>
                        <execution>
                            <id>multi-module-root-directory-site</id>
                            <goals>
                                <goal>directory-of</goal>
                            </goals>
                            <phase>pre-site</phase>
                            <configuration>
                                <property>multi.module.root</property>
                                <project>
                                    <groupId>net.postgis</groupId>
                                    <artifactId>postgis-java-aggregator</artifactId>
                                </project>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>pre-unit-test</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>unit-test-report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>pre-integration-test</id>
                            <goals>
                                <goal>prepare-agent-integration</goal>
                            </goals>
                            <configuration>
                                <destFile>${project.build.directory}/jacoco-it.exec</destFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>integration-test-report</id>
                            <phase>integration-test</phase>
                            <goals>
                                <goal>report-integration</goal>
                            </goals>
                            <configuration>
                                <dataFile>${project.build.directory}/jacoco-it.exec</dataFile>
                                <outputDirectory>${project.reporting.outputDirectory}/jacoco-it</outputDirectory>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>${git-commit-id-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>git-describe</id>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                            <configuration>
                                <generateGitPropertiesFile>true</generateGitPropertiesFile>
                                <generateGitPropertiesFilename>${project.build.outputDirectory}/META-INF/git.properties</generateGitPropertiesFilename>
                                <includeOnlyProperties>
                                    <property>git.branch$</property>
                                    <!--<property>git.build.host$</property>-->
                                    <property>git.build.number$</property>
                                    <property>git.build.number.unique$</property>
                                    <property>git.build.time$</property>
                                    <property>git.build.user.email$</property>
                                    <property>git.build.user.name$</property>
                                    <property>git.build.version$</property>
                                    <property>git.closest.tag.commit.count$</property>
                                    <property>git.closest.tag.name$</property>
                                    <property>git.commit.id$</property>
                                    <property>git.commit.id.abbrev$</property>
                                    <property>git.commit.user.email$</property>
                                    <property>git.commit.user.name$</property>
                                    <property>git.commit.time$</property>
                                    <property>git.tags$</property>
                                </includeOnlyProperties>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.commonjava.maven.plugins</groupId>
                <artifactId>directory-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>javadoc-aggregated</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate</report>
                            <report>test-aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>jxr-aggregated</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate</report>
                            <report>test-aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>spotbugs</id>
                        <inherited>true</inherited>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                        <reports>
                            <report>spotbugs</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>checkstyle-aggregated</id>
                        <inherited>false</inherited>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                        <reports>
                            <report>checkstyle-aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>pmd</id>
                        <inherited>false</inherited>
                        <configuration>
                            <aggregate>true</aggregate>
                            <skip>false</skip>
                            <rulesets>
                                <ruleset>pmd.xml</ruleset>
                            </rulesets>
                        </configuration>
                        <reports>
                            <report>pmd</report>
                            <report>cpd</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>surefire</id>
                        <reports>
                            <report>report-only</report>
                            <report>failsafe-report-only</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>version-update-reports</id>
                        <reports>
                            <report>dependency-updates-report</report>
                            <report>plugin-updates-report</report>
                            <report>property-updates-report</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>test-coverage-report</id>
                        <reports>
                            <report>report</report>
                            <report>report-integration</report>
                            <report>report-aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <!-- This profile isn't tested on windows, the path may not be specified correctly. -->
        <profile>
            <id>windows-javadoc</id>
            <activation>
                <os>
                    <family>windows</family>
                </os>
            </activation>
            <properties>
                <javadoc.path>${java.home}/bin/javadoc.exe</javadoc.path>
            </properties>
        </profile>
        <profile>
            <id>javahome-env-javadoc</id>
            <activation>
                <property>
                    <name>env.JAVA_HOME</name>
                </property>
            </activation>
            <properties>
                <javadoc.path>${env.JAVA_HOME}/bin/javadoc</javadoc.path>
            </properties>
        </profile>
        <profile>
            <id>check-versions</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>versions-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>check-versions</id>
                                <goals>
                                    <goal>display-parent-updates</goal>
                                    <goal>display-plugin-updates</goal>
                                    <goal>display-dependency-updates</goal>
                                    <goal>display-property-updates</goal>
                                </goals>
                                <phase>validate</phase>
                                <configuration>
                                    <!--suppress MavenModelInspection -->
                                    <rulesUri>file:///${multi.module.root}/maven-version-rules.xml</rulesUri>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!--
        The release-sign-artifacts and sonatype deployment profiles only need activated when pushing snapshots or
        releases to maven central.  When enabled, additional artifacts will be generated for javadoc and source as well
        as GPG signatures for each artifact.  This profiles assume that the properties for the gpg plugin
        (such as gpg.keyname and gpg.passphrase are either defined in a settings.xml file or similar facility.
        -->
        <profile>
            <id>release-sign-artifacts</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>sonatype-deployment</id>
            <distributionManagement>
                <snapshotRepository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/content/repositories/snapshots</url>
                </snapshotRepository>
                <repository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                </repository>
            </distributionManagement>
        </profile>

        <profile>
            <id>SkipUnitTests</id>
            <properties>
                <maven.test.skip>true</maven.test.skip>
                <maven.integration.test.skip>true</maven.integration.test.skip>
            </properties>
        </profile>
        <profile>
            <id>SkipIntegrationTests</id>
            <properties>
                <maven.integration.test.skip>true</maven.integration.test.skip>
            </properties>
        </profile>
    </profiles>

</project>
