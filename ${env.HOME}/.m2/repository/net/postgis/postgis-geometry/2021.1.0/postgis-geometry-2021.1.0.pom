<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>net.postgis</groupId>
        <artifactId>postgis-java-aggregator</artifactId>
        <version>2021.1.0</version>
    </parent>

    <artifactId>postgis-geometry</artifactId>
    <version>2021.1.0</version>
    <packaging>jar</packaging>

    <name>PostGIS Geometry</name>
    <description>Geometry classes provided by PostGIS JDBC Extension</description>

    <properties />

    <dependencies>
        <dependency>
            <groupId>net.postgis.tools</groupId>
            <artifactId>test-utils</artifactId>
            <version>2021.1.0</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Automatic-Module-Name>net.postgis.jdbc.geometry</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
