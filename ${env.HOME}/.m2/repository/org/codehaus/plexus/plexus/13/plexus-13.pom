<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright The Codehaus Foundation.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus</artifactId>
  <version>13</version>
  <packaging>pom</packaging>

  <name>Plexus</name>
  <description>The Plexus project provides a full software stack for creating and executing software projects.</description>
  <url>https://codehaus-plexus.github.io/plexus-pom/</url>
  <inceptionYear>2001</inceptionYear>
  <organization>
    <name>Codehaus Plexus</name>
    <url>https://codehaus-plexus.github.io/</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>jvanzyl</id>
      <name>Jason van Zyl</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
        <role>Release Manager</role>
      </roles>
    </developer>
    <developer>
      <id>kaz</id>
      <name>Pete Kazmier</name>
      <email />
      <organization />
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jtaylor</id>
      <name>James Taylor</name>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>dandiep</id>
      <name>Dan Diephouse</name>
      <email><EMAIL></email>
      <organization>Envoi solutions</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>kasper</id>
      <name>Kasper Nielsen</name>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>bwalding</id>
      <name>Ben Walding</name>
      <email><EMAIL></email>
      <organization>Walding Consulting Services</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>mhw</id>
      <name>Mark Wilkinson</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>michal</id>
      <name>Michal Maczka</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>evenisse</id>
      <name>Emmanuel Venisse</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>trygvis</id>
      <name>Trygve Laugstøl</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>kenney</id>
      <name>Kenney Westerhof</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>carlos</id>
      <name>Carlos Sanchez</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>brett</id>
      <name>Brett Porter</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jdcasey</id>
      <name>John Casey</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>handyande</id>
      <name>Andrew Williams</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>rahul</id>
      <name>Rahul Thakur</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>joakime</id>
      <name>Joakim Erdfelt</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>olamy</id>
      <name>Olivier Lamy</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>hboutemy</id>
      <name>Hervé Boutemy</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>oleg</id>
      <name>Oleg Gusakov</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>vsiveton</id>
      <name>Vincent Siveton</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>krosenvold</id>
      <name>Kristian Rosenvold</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>agudian</id>
      <name>Andreas Gudian</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>khmarbaise</id>
      <name>Karl Heinz Marbaise</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>michael-o</id>
      <name>Michael Osipov</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>belingueres</id>
      <name>Gabriel Belingueres</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>kwin</id>
      <name>Konrad Windszus</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>sjaranowski</id>
      <name>Slawomir Jaranowski</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>slachiewicz</id>
      <name>Sylwester Lachiewicz</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>gnodet</id>
      <name>Guillaume Nodet</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <mailingLists>
    <mailingList>
      <name>Plexus and MojoHaus Development List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://groups.google.com/forum/#!forum/mojohaus-dev</archive>
    </mailingList>
    <mailingList>
      <name>Former (pre-2015-06) Development List</name>
      <archive>https://markmail.org/list/org.codehaus.plexus.dev</archive>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:https://github.com/codehaus-plexus/plexus-pom.git</connection>
    <developerConnection>${project.scm.connection}</developerConnection>
    <tag>plexus-13</tag>
    <url>https://github.com/codehaus-plexus/plexus-pom/tree/master/</url>
  </scm>

  <issueManagement>
    <system>github</system>
    <url>https://github.com/codehaus-plexus/plexus-pom/issues</url>
  </issueManagement>

  <distributionManagement>
    <repository>
      <id>plexus-releases</id>
      <name>Plexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>plexus-snapshots</id>
      <name>Plexus Snapshot Repository</name>
      <url>${plexusDistMgmtSnapshotsUrl}</url>
    </snapshotRepository>
    <!-- site distribution management must be override in child projects -->
    <site>
      <id>github:gh-pages</id>
      <url>${project.scm.developerConnection}</url>
    </site>
  </distributionManagement>

  <properties>
    <javaVersion>8</javaVersion>
    <maven.compiler.source>1.${javaVersion}</maven.compiler.source>
    <maven.compiler.target>1.${javaVersion}</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <plexusDistMgmtSnapshotsUrl>https://oss.sonatype.org/content/repositories/plexus-snapshots</plexusDistMgmtSnapshotsUrl>
    <project.build.outputTimestamp>2023-05-22T15:02:14Z</project.build.outputTimestamp>
    <gpg.useagent>true</gpg.useagent>
    <spotless-maven-plugin.version>2.36.0</spotless-maven-plugin.version>
    <mavenPluginToolsVersion>3.9.0</mavenPluginToolsVersion>
    <mavenFluidoSkinVersion>1.11.2</mavenFluidoSkinVersion>
    <junit5Version>5.9.3</junit5Version>
    <spotless.action>check</spotless.action>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.maven.plugin-tools</groupId>
        <artifactId>maven-plugin-annotations</artifactId>
        <version>${mavenPluginToolsVersion}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit5Version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <!-- set versions of common plugins for reproducibility, ordered alphabetically -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.6.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>3.2.2</version>
          <configuration>
            <configLocation>config/maven_checks.xml</configLocation>
            <!-- version from release 11 referenced by branch -->
            <headerLocation>https://raw.githubusercontent.com/codehaus-plexus/plexus-pom/plexus-11/src/main/resources/config/plexus-header.txt</headerLocation>
          </configuration>
          <dependencies>
            <!-- MCHECKSTYLE-327: the maven_checks.xml was moved to a shared project -->
            <dependency>
              <groupId>org.apache.maven.shared</groupId>
              <artifactId>maven-shared-resources</artifactId>
              <version>5</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.11.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>3.1.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.3.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>3.1.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.3.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.5.0</version>
          <configuration>
            <quiet>true</quiet>
            <locale>en</locale>
            <!-- avoid noise for svn/gitpubsub -->
            <notimestamp>true</notimestamp>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>3.3.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${mavenPluginToolsVersion}</version>
          <executions>
            <execution>
              <id>default-descriptor</id>
              <phase>process-classes</phase>
              <configuration>
                <internalJavadocBaseUrl>./apidocs/</internalJavadocBaseUrl>
              </configuration>
            </execution>
            <execution>
              <id>generate-helpmojo</id>
              <goals>
                <goal>helpmojo</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-report-plugin</artifactId>
          <version>${mavenPluginToolsVersion}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>3.21.0</version>
          <configuration>
            <targetJdk>${maven.compiler.source}</targetJdk>
            <rulesets>
              <ruleset>rulesets/maven.xml</ruleset>
            </rulesets>
            <excludeRoots>
              <excludeRoot>${project.build.directory}/generated-sources/modello</excludeRoot>
              <excludeRoot>${project.build.directory}/generated-sources/plugin</excludeRoot>
            </excludeRoots>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>3.4.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>3.0.0</version>
          <configuration>
            <goals>deploy</goals>
            <mavenExecutorId>forked-path</mavenExecutorId>
            <releaseProfiles>plexus-release</releaseProfiles>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>3.2.1</version>
          <configuration>
            <!-- using scm.developerConnection instead of distributionManagement.site.url -->
            <pubScmUrl>${project.scm.developerConnection}</pubScmUrl>
            <scmBranch>gh-pages</scmBranch>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.12.1</version>
          <configuration>
            <!-- don't deploy site with maven-site-plugin -->
            <skipDeploy>true</skipDeploy>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.2.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>taglist-maven-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>2.1.1</version>
          <executions>
            <execution>
              <id>process-classes</id>
              <goals>
                <goal>generate-metadata</goal>
              </goals>
            </execution>
            <execution>
              <id>process-test-classes</id>
              <goals>
                <goal>generate-test-metadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <version>${spotless-maven-plugin.version}</version>
          <configuration>
            <java>
              <!-- orders of used formatters are important -->
              <!-- eg. palantir override importOrder, so should be first -->
              <palantirJavaFormat />
              <removeUnusedImports />
              <importOrder>
                <order>javax,java,,\#</order>
              </importOrder>
            </java>
            <pom>
              <sortPom>
                <expandEmptyElements>false</expandEmptyElements>
                <!-- https://issues.apache.org/jira/browse/MRELEASE-1111 -->
                <spaceBeforeCloseEmptyElement>true</spaceBeforeCloseEmptyElement>
              </sortPom>
            </pom>
            <upToDateChecking>
              <enabled>true</enabled>
            </upToDateChecking>
          </configuration>
          <executions>
            <execution>
              <id>spotless-check</id>
              <goals>
                <goal>${spotless.action}</goal>
              </goals>
              <phase>process-sources</phase>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <dependencies>
          <dependency>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>extra-enforcer-rules</artifactId>
            <version>1.6.2</version>
          </dependency>
        </dependencies>
        <executions>
          <execution>
            <id>enforce-maven-and-java-bytecode</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.2.5</version>
                  <message>This project requires at least Maven 3.2.5</message>
                </requireMavenVersion>
                <enforceBytecodeVersion>
                  <maxJdkVersion>${maven.compiler.target}</maxJdkVersion>
                </enforceBytecodeVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-descriptor</id>
            <goals>
              <goal>attach-descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>summary</report>
              <report>dependency-info</report>
              <report>modules</report>
              <report>licenses</report>
              <report>team</report>
              <report>scm</report>
              <report>issue-management</report>
              <report>mailing-lists</report>
              <report>dependency-management</report>
              <report>dependencies</report>
              <report>dependency-convergence</report>
              <report>ci-management</report>
              <report>plugin-management</report>
              <report>plugins</report>
              <report>distribution-management</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-project-info-reports-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-report-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>taglist-maven-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <reportSets>
              <reportSet>
                <id>default</id>
                <reports>
                  <report>jxr</report>
                  <report>test-jxr</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <reportSets>
              <reportSet>
                <id>default</id>
                <reports>
                  <report>javadoc</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
    <profile>
      <id>plexus-release</id>
      <build>
        <plugins>
          <!-- Create a source-release artifact that contains the fully buildable
               project directory source structure. -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.apache.resources</groupId>
                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                <version>1.5</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>source-release-assembly</id>
                <goals>
                  <goal>single</goal>
                </goals>
                <phase>package</phase>
                <configuration>
                  <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                  <descriptorRefs>
                    <descriptorRef>source-release</descriptorRef>
                  </descriptorRefs>
                  <tarLongFileMode>posix</tarLongFileMode>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>java11+</id>
      <activation>
        <jdk>[11,)</jdk>
      </activation>

      <properties>
        <maven.compiler.release>${javaVersion}</maven.compiler.release>
        <!-- configuration for checkstyle to use with spotless -->
        <checkstyle.spotless.config>config/maven_checks_nocodestyle.xml</checkstyle.spotless.config>
      </properties>

      <build>
        <!--- newer versions of plugins requires JDK 11 -->
        <plugins>
          <plugin>
            <groupId>com.diffplug.spotless</groupId>
            <artifactId>spotless-maven-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <configuration>
              <configLocation>${checkstyle.spotless.config}</configLocation>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>format-check</id>
      <activation>
        <property>
          <name>!format</name>
        </property>
      </activation>
      <properties>
        <spotless.action>check</spotless.action>
      </properties>
    </profile>
    <profile>
      <id>format</id>
      <activation>
        <property>
          <name>format</name>
        </property>
      </activation>
      <properties>
        <spotless.action>apply</spotless.action>
      </properties>
    </profile>
  </profiles>
</project>
