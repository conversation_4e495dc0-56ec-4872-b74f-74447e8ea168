<?xml version="1.0" encoding="UTF-8"?>

<!--
Copyright The Codehaus Foundation.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>2.0.2</version>
    <relativePath>../pom/pom.xml</relativePath>
  </parent>

  <artifactId>plexus-utils</artifactId>
  <version>1.5.15</version>

  <name>Plexus Common Utilities</name>
  <description>A collection of various utility classes to ease working with strings, files, command lines, XML and more.</description>
  <url>http://plexus.codehaus.org/plexus-utils</url>

  <scm>
    <connection>scm:svn:http://svn.codehaus.org/plexus/plexus-utils/tags/plexus-utils-1.5.15</connection>
    <developerConnection>scm:svn:https://svn.codehaus.org/plexus/plexus-utils/tags/plexus-utils-1.5.15</developerConnection>
    <url>http://fisheye.codehaus.org/browse/plexus/plexus-utils/tags/plexus-utils-1.5.15</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>http://jira.codehaus.org/browse/PLXUTILS</url>
  </issueManagement>

  <dependencies>
    <dependency>
      <!-- NOTE: plexus-interpolation is SHADED INTO plexus-utils.
           This is done to provide a migration path from the old
           interpolation classes in plexus-utils to the current
           implementation, which was broken out into plexus-interpolation.
           The old classes still exist in plexus-utils, but are 
           DEPRECATED.
      -->
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-interpolation</artifactId>
      <version>1.11</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-classworlds</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>1.0.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.0-beta-7</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <!-- surefire requires plexus-utils to be jdk 1.3 compatible -->
          <source>1.3</source>
          <target>1.3</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!-- required to ensure the test classes are used, not surefire's plexus-utils -->
          <childDelegation>true</childDelegation>
          <excludes>
            <exclude>org/codehaus/plexus/util/FileBasedTestCase.java</exclude>
            <exclude>**/Test*.java</exclude>
          </excludes>
          <systemProperties>
            <property>
              <name>JAVA_HOME</name>
              <value>${JAVA_HOME}</value>
            </property>
            <property>
              <name>M2_HOME</name>
              <value>${M2_HOME}</value>
            </property>
          </systemProperties>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <id>shade</id>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <includes>
                  <include>org.codehaus.plexus:plexus-interpolation</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>org.codehaus.plexus.interpolation</pattern>
                </relocation>
              </relocations>
              <keepDependenciesWithProvidedScope>true</keepDependenciesWithProvidedScope>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <useReleaseProfile>true</useReleaseProfile>
          <tagBase>https://svn.codehaus.org/plexus/plexus-utils/tags/</tagBase>
          <arguments>-Prelease</arguments>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <links>
            <link>http://java.sun.com/j2se/1.4.2/docs/api/</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>
</project>
