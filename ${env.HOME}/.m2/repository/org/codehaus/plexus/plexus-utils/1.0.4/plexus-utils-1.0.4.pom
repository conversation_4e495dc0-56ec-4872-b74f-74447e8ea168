<project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus-utils</artifactId>
  <name>Plexus Common Utilities</name>
  <version>1.0.4</version>
  <ciManagement>
    <notifiers>
      <notifier>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
      <notifier>
        <type>irc</type>
        <configuration>
          <port>6667</port>
          <channel>#plexus</channel>
          <host>irc.codehaus.org</host>
        </configuration>
      </notifier>
    </notifiers>
  </ciManagement>
  <inceptionYear>2001</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>Plexus Developer List</name>
      <subscribe>http://lists.codehaus.org/mailman/listinfo/plexus-dev</subscribe>
      <unsubscribe>http://lists.codehaus.org/mailman/listinfo/plexus-dev</unsubscribe>
      <archive>http://lists.codehaus.org/pipermail/plexus-dev/</archive>
    </mailingList>
  </mailingLists>
  <developers>
    <developer>
      <id>jvanzyl</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>Zenplex</organization>
      <roles>
        <role>Developer</role>
        <role>Release Manager</role>
      </roles>
    </developer>
    <developer>
      <id>kaz</id>
      <name>Pete Kazmier</name>
      <email></email>
      <organization></organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jtaylor</id>
      <name>James Taylor</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>dandiep</id>
      <name>Dan Diephouse</name>
      <email><EMAIL></email>
      <organization>Envoi solutions</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>kasper</id>
      <name>Kasper Nielsen</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>bwalding</id>
      <name>Ben Walding</name>
      <email><EMAIL></email>
      <organization>Walding Consulting Services</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>mhw</id>
      <name>Mark Wilkinson</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>michal</id>
      <name>Michal Maczka</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>evenisse</id>
      <name>Emmanuel Venisse</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>trygvis</id>
      <name>Trygve Laugstol</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>kenney</id>
      <name>Kenney Westerhof</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:svn:svn://svn.codehaus.org/plexus/scm/trunk/plexus-utils</connection>
    <developerConnection>scm:svn:https://svn.codehaus.org/plexus/trunk/plexus-utils</developerConnection>
  </scm>
  <organization>
    <name>Codehaus</name>
    <url>http://www.codehaus.org/</url>
  </organization>
  <build>
    <sourceDirectory>src/main/java</sourceDirectory>
    <scriptSourceDirectory>src/main/scripts</scriptSourceDirectory>
    <testSourceDirectory>src/test/java</testSourceDirectory>
    <outputDirectory>target/classes</outputDirectory>
    <testOutputDirectory>target/test-classes</testOutputDirectory>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>src/test/resources</directory>
      </testResource>
    </testResources>
    <directory>target</directory>
    <plugins>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.0-beta-3-SNAPSHOT</version>
        <configuration>
          <tagBase>https://svn.codehaus.org/plexus/tags</tagBase>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>RELEASE</version>
        <configuration>
          <excludes>
            <exclude implementation="java.lang.String">org/codehaus/plexus/util/FileBasedTestCase.java</exclude>
            <exclude implementation="java.lang.String">**/Test*.java</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <id>snapshots</id>
      <name>Maven Snapshot Development Repository</name>
      <url>http://snapshots.maven.codehaus.org/maven2</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Maven Repository Switchboard</name>
      <url>http://repo1.maven.org/maven2</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <id>snapshots-plugins</id>
      <name>Maven Snapshot Plugins Development Repository</name>
      <url>http://snapshots.maven.codehaus.org/maven2</url>
    </pluginRepository>
    <pluginRepository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Maven Plugin Repository</name>
      <url>http://repo1.maven.org/maven2</url>
    </pluginRepository>
  </pluginRepositories>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <reporting>
    <outputDirectory>target/site</outputDirectory>
  </reporting>
  <distributionManagement>
    <repository>
      <id>repo1</id>
      <name>Maven Central Repository</name>
      <url>scp://repo1.maven.org/home/<USER>/maven/repository-staging/to-ibiblio/maven2</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>Maven Central Development Repository</name>
      <url>scp://repo1.maven.org/home/<USER>/maven/repository-staging/snapshots/maven2</url>
    </snapshotRepository>
    <status>deployed</status>
  </distributionManagement>
</project>