<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>39</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.commons</groupId>
  <artifactId>commons-math3</artifactId>
  <version>3.6.1</version>
  <name>Apache Commons Math</name>

  <inceptionYear>2003</inceptionYear>
  <description>The Apache Commons Math project is a library of lightweight, self-contained mathematics and statistics components addressing the most common practical problems not immediately available in the Java programming language or commons-lang.</description>

  <url>http://commons.apache.org/proper/commons-math/</url>

  <issueManagement>
    <system>jira</system>
    <url>http://issues.apache.org/jira/browse/MATH</url>
  </issueManagement>

  <scm>
    <connection>scm:git:http://git-wip-us.apache.org/repos/asf/commons-math.git</connection>
    <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/commons-math.git</developerConnection>
    <url>https://git-wip-us.apache.org/repos/asf?p=commons-math.git</url>
  </scm>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Commons Site</name>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-math/</url>
    </site>
  </distributionManagement>

  <developers>
    <developer>
      <name>Mikkel Meyer Andersen</name>
      <id>mikl</id>
      <email>mikl at apache dot org</email>
    </developer>
    <developer>
      <name>Bill Barker</name>
      <id>billbarker</id>
      <email>billbarker at apache dot org</email>
    </developer>
    <developer>
      <name>S&#233;bastien Brisard</name>
      <id>celestin</id>
      <email>celestin at apache dot org</email>
    </developer>
    <developer>
      <name>Albert Davidson Chou</name>
      <id>achou</id>
      <email>achou at apache dot org</email>
    </developer>
    <developer>
      <name>Mark Diggory</name>
      <id>mdiggory</id>
      <email>mdiggory at apache dot org</email>
    </developer>
    <developer>
      <name>Robert Burrell Donkin</name>
      <id>rdonkin</id>
      <email>rdonkin at apache dot org</email>
    </developer>
    <developer>
      <name>Otmar Ertl</name>
      <id>oertl</id>
      <email>oertl at apache dot org</email>
    </developer>
    <developer>
      <name>Luc Maisonobe</name>
      <id>luc</id>
      <email>luc at apache dot org</email>
    </developer>
    <developer>
      <name>Tim O'Brien</name>
      <id>tobrien</id>
      <email>tobrien at apache dot org</email>
    </developer>
    <developer>
      <name>J. Pietschmann</name>
      <id>pietsch</id>
      <email>j3322ptm at yahoo dot de</email>
    </developer>
    <developer>
      <name>Dimitri Pourbaix</name>
      <id>dimpbx</id>
      <email>dimpbx at apache dot org</email>
    </developer>
    <developer>
      <name>Gilles Sadowski</name>
      <id>erans</id>
      <email>erans at apache dot org</email>
    </developer>
    <developer>
      <name>Greg Sterijevski</name>
      <id>gregs</id>
      <email>gregs at apache dot org</email>
    </developer>
    <developer>
      <name>Brent Worden</name>
      <id>brentworden</id>
      <email>brentworden at apache dot org</email>
    </developer>
    <developer>
      <name>Thomas Neidhart</name>
      <id>tn</id>
      <email>tn at apache dot org</email>
    </developer>
    <developer>
      <name>Evan Ward</name>
      <id>evanward</id>
      <email>evanward at apache dot org</email>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <name>Eldar Agalarov</name>
    </contributor>
    <contributor>
      <name>Tim Allison</name>
    </contributor>
    <contributor>
      <name>C. Scott Ananian</name>
    </contributor>
    <contributor>
      <name>Mark Anderson</name>
    </contributor>
    <contributor>
      <name>Peter Andrews</name>
    </contributor>
    <contributor>
      <name>R&#233;mi Arntzen</name>
    </contributor>
    <contributor>
      <name>Matt Adereth</name>
    </contributor>
    <contributor>
      <name>Jared Becksfort</name>
    </contributor>
    <contributor>
      <name>Michael Bjorkegren</name>
    </contributor>
    <contributor>
      <name>Brian Bloniarz</name>
    </contributor>
    <contributor>
      <name>John Bollinger</name>
    </contributor>
    <contributor>
      <name>Cyril Briquet</name>
    </contributor>
    <contributor>
      <name>Dave Brosius</name>
    </contributor>
    <contributor>
      <name>Dan Checkoway</name>
    </contributor>
    <contributor>
      <name>Anders Conbere</name>
    </contributor>
    <contributor>
      <name>Charles Cooper</name>
    </contributor>
    <contributor>
      <name>Paul Cowan</name>
    </contributor>
    <contributor>
      <name>Benjamin Croizet</name>
    </contributor>
    <contributor>
      <name>Larry Diamond</name>
    </contributor>
    <contributor>
      <name>Aleksei Dievskii</name>
    </contributor>
    <contributor>
      <name>Rodrigo di Lorenzo Lopes</name>
    </contributor>
    <contributor>
      <name>Hasan Diwan</name>
    </contributor>
    <contributor>
      <name>Ted Dunning</name>
    </contributor>
    <contributor>
      <name>Ole Ersoy</name>
    </contributor>
    <contributor>
      <name>Ajo Fod</name>
    </contributor>
    <contributor>
      <name>John Gant</name>
    </contributor>
    <contributor>
      <name>Ken Geis</name>
    </contributor>
    <contributor>
      <name>Hank Grabowski</name>
    </contributor>
    <contributor>
      <name>Bernhard Gr&#252;newaldt</name>
    </contributor>
    <contributor>
      <name>Elliotte Rusty Harold</name>
    </contributor>
    <contributor>
      <name>Dennis Hendriks</name>
    </contributor>
    <contributor>
      <name>Reid Hochstedler</name>
    </contributor>
    <contributor>
      <name>Matthias Hummel</name>
    </contributor>
    <contributor>
      <name>Curtis Jensen</name>
    </contributor>
    <contributor>
      <name>Bruce A Johnson</name>
    </contributor>
    <contributor>
      <name>Ismael Juma</name>
    </contributor>
    <contributor>
      <name>Eugene Kirpichov</name>
    </contributor>
    <contributor>
      <name>Oleksandr Kornieiev</name>
    </contributor>
    <contributor>
      <name>Piotr Kochanski</name>
    </contributor>
    <contributor>
      <name>Sergei Lebedev</name>
    </contributor>
    <contributor>
      <name>Bob MacCallum</name>
    </contributor>
    <contributor>
      <name>Jake Mannix</name>
    </contributor>
    <contributor>
      <name>Benjamin McCann</name>
    </contributor>
    <contributor>
      <name>Patrick Meyer</name>
    </contributor>
    <contributor>
      <name>J. Lewis Muir</name>
    </contributor>
    <contributor>
      <name>Venkatesha Murthy</name>
    </contributor>
    <contributor>
      <name>Christopher Nix</name>
    </contributor>
    <contributor>
      <name>Fredrik Norin</name>
    </contributor>
    <contributor>
      <name>Sean Owen</name>
    </contributor>
    <contributor>
      <name>Sujit Pal</name>
    </contributor>
    <contributor>
      <name>Todd C. Parnell</name>
    </contributor>
    <contributor>
      <name>Andreas Rieger</name>
    </contributor>
    <contributor>
      <name>S&#233;bastien Riou</name>
    </contributor>
    <contributor>
      <name>Bill Rossi</name>
    </contributor>
    <contributor>
      <name>Matthew Rowles</name>
    </contributor>
    <contributor>
      <name>Pavel Ryzhov</name>
    </contributor>
    <contributor>
      <name>Joni Salonen</name>
    </contributor>
    <contributor>
      <name>Michael Saunders</name>
    </contributor>
    <contributor>
      <name>Thorsten Schaefer</name>
    </contributor>
    <contributor>
      <name>Christopher Schuck</name>
    </contributor>
    <contributor>
      <name>Christian Semrau</name>
    </contributor>
    <contributor>
      <name>David Stefka</name>
    </contributor>
    <contributor>
      <name>Mauro Talevi</name>
    </contributor>
    <contributor>
      <name>Radoslav Tsvetkov</name>
    </contributor>
    <contributor>
      <name>Kim van der Linde</name>
    </contributor>
    <contributor>
      <name>Alexey Volkov</name>
    </contributor>
    <contributor>
      <name>Andrew Waterman</name>
    </contributor>
    <contributor>
      <name>J&#246;rg Weimar</name>
    </contributor>
    <contributor>
      <name>Christian Winter</name>
    </contributor>
    <contributor>
      <name>Piotr Wydrych</name>
    </contributor>
    <contributor>
      <name>Xiaogang Zhang</name>
    </contributor>
    <contributor>
      <name>Chris Popp</name>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <properties>
    <!-- Do not change: "math" is the name of the component even if the
         name of the base package evolves with major release numbers
         (see "commons.osgi.symbolicName", below). -->
    <commons.componentid>math</commons.componentid>
    <!-- This value must reflect the current name of the base package. -->
    <commons.osgi.symbolicName>org.apache.commons.math3</commons.osgi.symbolicName>
    <!-- do not use snapshot suffix here -->
    <commons.release.version>3.6.1</commons.release.version>
    <commons.release.desc>(requires Java 1.5+)</commons.release.desc>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.binary.suffix>-bin</commons.binary.suffix>

    <commons.release.2.version>2.2</commons.release.2.version>
    <!-- override parent name, because 2.2 uses different artifactId -->
    <commons.release.2.name>commons-math-${commons.release.2.version}</commons.release.2.name>
    <commons.release.2.desc>(requires Java 1.5+)</commons.release.2.desc>
    <commons.release.2.binary.suffix></commons.release.2.binary.suffix>

    <commons.jira.id>MATH</commons.jira.id>
    <commons.jira.pid>12310485</commons.jira.pid>
    <commons.encoding>UTF-8</commons.encoding>
    <maven.compiler.source>1.5</maven.compiler.source>
    <maven.compiler.target>1.5</maven.compiler.target>
    <math.pmd.version>3.5</math.pmd.version>
    <math.findbugs.version>3.0.2</math.findbugs.version>
    <math.checkstyle.version>2.17</math.checkstyle.version>
    <math.clirr.version>2.6.1</math.clirr.version>

    <!-- Temporary fix to support Java 8 -->
    <commons.jacoco.version>0.7.5.201505241946</commons.jacoco.version>
    <commons.jacoco.classRatio>0.96</commons.jacoco.classRatio>
    <commons.jacoco.instructionRatio>0.8</commons.jacoco.instructionRatio>
    <commons.jacoco.methodRatio>0.8</commons.jacoco.methodRatio>
    <commons.jacoco.branchRatio>0.8</commons.jacoco.branchRatio>
    <commons.jacoco.complexityRatio>0.8</commons.jacoco.complexityRatio>
    <commons.jacoco.lineRatio>0.85</commons.jacoco.lineRatio>
    <commons.jacoco.haltOnFailure>false</commons.jacoco.haltOnFailure>

    <commons.site.path>math</commons.site.path>
    <commons.scmPubUrl>https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-math</commons.scmPubUrl>
    <commons.scmPubCheckoutDirectory>site-content</commons.scmPubCheckoutDirectory>

    <!-- Temporary fix to replace svn-based build number with git-based build number -->
    <buildnumber.skip>true</buildnumber.skip>
    <math.jgit.buildnumber.version>1.2.10</math.jgit.buildnumber.version>
    <implementation.build>${git.revision}; ${maven.build.timestamp}</implementation.build>

  </properties>

  <build>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <includes>
                <include>**/*Test.java</include>
                <include>**/*TestBinary.java</include>
                <include>**/*TestPermutations.java</include>
              </includes>
              <excludes>
                <exclude>**/*AbstractTest.java</exclude>
              </excludes>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <configuration>
            <descriptors>
              <descriptor>src/main/assembly/src.xml</descriptor>
              <descriptor>src/main/assembly/bin.xml</descriptor>
            </descriptors>
            <!-- There are a lot of long file names. Suppress the warnings. -->
            <tarLongFileMode>gnu</tarLongFileMode>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>${math.clirr.version}</version>
          <configuration>
            <minSeverity>${minSeverity}</minSeverity>
            <ignoredDifferencesFile>${basedir}/clirr-ignored.xml</ignoredDifferencesFile>
          </configuration>
          <executions>
            <execution>
              <goals>
              </goals>
            </execution>
          </executions>
        </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${math.pmd.version}</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
          <skipEmptyReport>false</skipEmptyReport>
          <rulesets>
            <ruleset>${basedir}/pmd-ruleset.xml</ruleset>
          </rulesets>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <ignorePathsToDelete>
            <ignorePathToDelete>javadocs</ignorePathToDelete>
          </ignorePathsToDelete>
        </configuration>
      </plugin>

      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <configuration>
              <target>
                <jar destfile="${project.build.directory}/${project.artifactId}-${project.version}-tools.jar">
                  <metainf dir="${basedir}" includes="NOTICE.txt,LICENSE.txt" />
                  <manifest>
                    <attribute name="Specification-Title" value="${project.name} Tools" />
                    <attribute name="Implementation-Title" value="${project.name} Tools" />
                    <attribute name="Implementation-Vendor" value="${project.organization.name}" />
                    <attribute name="Implementation-Version" value="${project.version}" />
                    <attribute name="Implementation-Vendor-Id" value="org.apache" />
                    <attribute name="Implementation-Build" value="${implementation.build}"/>
                    <attribute name="X-Compile-Source-JDK" value="${maven.compiler.source}" />
                    <attribute name="X-Compile-Target-JDK" value="${maven.compiler.target}" />
                  </manifest>
                  <fileset dir="${project.build.directory}/test-classes"
                           includes="org/apache/commons/math3/PerfTestUtils*" />
                </jar>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!--  Attaches the commons-math3 tools JAR to the Maven lifecycle
            to ensure they will be signed and deployed as normal -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>1.7</version>
        <executions>
          <execution>
            <id>attach-artifacts</id>
            <phase>package</phase>
            <goals>
              <goal>attach-artifact</goal>
            </goals>
            <configuration>
              <artifacts>
                <artifact>
                  <file>${project.build.directory}/${project.artifactId}-${project.version}-tools.jar</file>
                  <type>jar</type>
                  <classifier>tools</classifier>
                </artifact>
              </artifacts>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <!--  MathJax -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <additionalparam>-header &apos;&lt;script type=&quot;text/javascript&quot; src=&quot;http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS-MML_HTMLorMML&quot;&gt;&lt;/script&gt;&apos;</additionalparam>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <version>${commons.changes.version}</version>
        <configuration>
          <issueLinkTemplatePerSystem>
            <default>%URL%/%ISSUE%</default>
          </issueLinkTemplatePerSystem>
          <!--  Add sample JIRA report - 'mvn changes:jira-report' or 'mvn site' -->
          <onlyCurrentVersion>false</onlyCurrentVersion>
          <columnNames>Fix Version,Key,Summary,Type,Resolution,Status</columnNames>
          <!-- Sort cols have to be reversed in JIRA 4 -->
          <sortColumnNames>Key DESC,Type,Fix Version DESC</sortColumnNames>
          <resolutionIds>Fixed</resolutionIds>
          <statusIds>Resolved,Closed</statusIds>
          <!-- Don't include sub-task -->
          <typeIds>Bug,New Feature,Task,Improvement,Wish,Test</typeIds>
          <fixVersionIds>${commons.release.version}</fixVersionIds>
          <!-- The default is 100 -->
          <maxEntries>100</maxEntries>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>changes-report</report>
              <report>jira-report</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>${commons.rat.version}</version>
        <configuration>
          <excludes>

            <!-- MANIFEST files cannot have any comments, so we can't put license header -->
            <exclude>src/test/maxima/special/RealFunctionValidation/MANIFEST.txt</exclude>

            <!-- the following are test data files with specific syntax that cannot include
                 Apache header (and the contained data is public, it is not owned by Apache) -->
            <exclude>src/test/resources/org/apache/commons/math3/random/testData.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/random/emptyFile.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/stat/data/PiDigits.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/stat/data/NumAcc3.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/stat/data/Lew.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/stat/data/NumAcc2.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/stat/data/NumAcc1.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/stat/data/Lottery.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/stat/data/NumAcc4.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/stat/data/Michelso.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/stat/data/Mavro.txt</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/geometry/euclidean/threed/issue-1211.bsp</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/geometry/euclidean/threed/pentomino-N-bad-orientation.ply</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/geometry/euclidean/threed/pentomino-N-hole.ply</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/geometry/euclidean/threed/pentomino-N-out-of-plane.ply</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/geometry/euclidean/threed/pentomino-N-too-close.ply</exclude>
            <exclude>src/test/resources/org/apache/commons/math3/geometry/euclidean/threed/pentomino-N.ply</exclude>

            <!-- direction numbers for Sobol generation from Frances Y. Kuo and Stephen Joe,
                 available under a BSD-style license (see LICENSE.txt) -->
            <exclude>src/main/resources/assets/org/apache/commons/math3/random/new-joe-kuo-6.1000</exclude>

            <!-- text file explaining reference to a public domain image -->
            <exclude>src/userguide/resources/references.txt</exclude>

            <!-- version 0.8 of apache-rat-plugin does not exclude properly
                 some default development tools files (see RAT-126) -->
            <exclude>bin/**</exclude>
            <exclude>.gitignore</exclude>
            <exclude>.git/**</exclude>
            <exclude>.checkstyle</exclude>
            <exclude>.ekstazi/**</exclude>

          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${math.findbugs.version}</version>
        <configuration>
          <threshold>Normal</threshold>
          <effort>Default</effort>
          <excludeFilterFile>${basedir}/findbugs-exclude-filter.xml</excludeFilterFile>
       </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${math.checkstyle.version}</version>
        <configuration>
          <configLocation>${basedir}/checkstyle.xml</configLocation>
          <enableRulesSummary>false</enableRulesSummary>
          <includeResources>false</includeResources>
          <headerLocation>${basedir}/license-header.txt</headerLocation>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>checkstyle</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <version>${math.clirr.version}</version>
        <configuration>
          <minSeverity>${minSeverity}</minSeverity>
          <ignoredDifferencesFile>${basedir}/clirr-ignored.xml</ignoredDifferencesFile>
         </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${math.pmd.version}</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
          <skipEmptyReport>false</skipEmptyReport>
          <rulesets>
            <ruleset>${basedir}/pmd-ruleset.xml</ruleset>
          </rulesets>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>pmd</report>
              <!-- As of 3.x series, the cpd report sees (correctly) numerous duplications -->
              <!-- This is due to packages being renamed, and the old name still needs to be -->
              <!-- available for compatibility. They will be removed in 4.0 -->
              <!-- So we temporarily disable the CPD report -->
              <!-- <report>cpd</report> -->
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <!--  MathJax -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <additionalparam>-header &apos;&lt;script type=&quot;text/javascript&quot; src=&quot;http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS-MML_HTMLorMML&quot;&gt;&lt;/script&gt;&apos;</additionalparam>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>jgit-buildnumber</id>
      <activation>
        <file>
          <exists>.git</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>ru.concerteza.buildnumber</groupId>
            <artifactId>maven-jgit-buildnumber-plugin</artifactId>
            <version>${math.jgit.buildnumber.version}</version>
            <executions>
              <execution>
                <phase>generate-resources</phase>
                <goals>
                  <goal>extract-buildnumber</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>setup-checkout</id>
      <activation>
        <file>
          <missing>site-content</missing>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <version>1.7</version>
            <executions>
              <execution>
                <id>prepare-checkout</id>
                <phase>pre-site</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <tasks>
                    <exec executable="svn">
                      <arg line="checkout --depth immediates ${commons.scmPubUrl} ${commons.scmPubCheckoutDirectory}" />
                    </exec>

                    <exec executable="svn">
                      <arg line="update --set-depth exclude ${commons.scmPubCheckoutDirectory}/javadocs" />
                    </exec>

                    <pathconvert pathsep=" " property="dirs">
                      <dirset dir="${commons.scmPubCheckoutDirectory}" includes="*" />
                    </pathconvert>
                    <exec executable="svn">
                      <arg line="update --set-depth infinity ${dirs}" />
                    </exec>
                  </tasks>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- Ekstazi (www.ekstazi.org) profile to optimize regression testing -->
    <profile>
      <id>ekstazi</id>
      <activation>
        <property>
          <name>ekstazi</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.ekstazi</groupId>
            <artifactId>ekstazi-maven-plugin</artifactId>
            <version>4.4.0</version>
            <configuration>
              <forcefailing>true</forcefailing>
            </configuration>
            <executions>
              <execution>
                <id>ekstazi</id>
                <goals>
                  <goal>select</goal>
                  <goal>restore</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludesFile>${java.io.tmpdir}/${user.name}EkstaziExcludes</excludesFile>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>

