<?xml version="1.0" encoding="UTF-8"?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j</artifactId>
    <version>2.17.1</version>
    <relativePath>../</relativePath>
  </parent>
  <artifactId>log4j-slf4j-impl</artifactId>
  <packaging>jar</packaging>
  <name>Apache Log4j SLF4J Binding</name>
  <description>The Apache Log4j SLF4J API binding to Log4j 2 Core</description>
  <properties>
    <log4jParentDir>${basedir}/..</log4jParentDir>
    <docLabel>SLF4J Documentation</docLabel>
    <projectDir>/slf4j-impl</projectDir>
    <!-- Do not upgrade the SLF4J version. 1.7.26 broke backward compatibility. Users can update the version if
      they do not require support for SLF4J's EventData -->
    <slf4j.version>1.7.25</slf4j.version>
    <module.name>org.apache.logging.log4j.slf4j</module.name>
    <maven.doap.skip>true</maven.doap.skip>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4j.version}</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-ext</artifactId>
      <version>${slf4j.version}</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-csv</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-to-slf4j</artifactId>
      <scope>test</scope>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <!-- Include the standard NOTICE and LICENSE -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-remote-resources-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>process</goal>
            </goals>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>default-jar</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration combine.self="override">
              <archive>
                <manifestFile>${manifestfile}</manifestFile>
                <manifestEntries>
                  <Specification-Title>${project.name}</Specification-Title>
                  <Specification-Version>${project.version}</Specification-Version>
                  <Specification-Vendor>${project.organization.name}</Specification-Vendor>
                  <Implementation-Title>${project.name}</Implementation-Title>
                  <Implementation-Version>${project.version}</Implementation-Version>
                  <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                  <Implementation-Vendor-Id>org.apache</Implementation-Vendor-Id>
                  <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                  <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                  <Automatic-Module-Name>${module.name}</Automatic-Module-Name>
                </manifestEntries>
              </archive>
            </configuration>
          </execution>
          <execution>
            <id>default</id>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <archive>
                <manifestFile>${manifestfile}</manifestFile>
                <manifestEntries>
                  <Specification-Title>${project.name}</Specification-Title>
                  <Specification-Version>${project.version}</Specification-Version>
                  <Specification-Vendor>${project.organization.name}</Specification-Vendor>
                  <Implementation-Title>${project.name}</Implementation-Title>
                  <Implementation-Version>${project.version}</Implementation-Version>
                  <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                  <Implementation-Vendor-Id>org.apache</Implementation-Vendor-Id>
                  <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                  <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                </manifestEntries>
              </archive>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <executions>
          <execution>
            <id>loop-test</id>
            <phase>test</phase>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <includes>
                <include>**/OverflowTest.java</include>
              </includes>
            </configuration>
          </execution>
          <execution>
            <id>default-test</id>
            <phase>test</phase>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <includes>
                <include>**/*Test.java</include>
              </includes>
              <excludes>
                <exclude>**/OverflowTest.java</exclude>
              </excludes>
              <classpathDependencyExcludes>
                <classpathDependencyExcludes>org.apache.logging.log4j:log4j-to-slf4j</classpathDependencyExcludes>
              </classpathDependencyExcludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <configuration>
          <instructions>
            <Export-Package>
              org.apache.logging.slf4j,
              org.slf4j.impl
            </Export-Package>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <version>${changes.plugin.version}</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>changes-report</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <issueLinkTemplate>%URL%/show_bug.cgi?id=%ISSUE%</issueLinkTemplate>
          <useJql>true</useJql>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${checkstyle.plugin.version}</version>
        <configuration>
          <!--<propertiesLocation>${vfs.parent.dir}/checkstyle.properties</propertiesLocation> -->
          <configLocation>${log4jParentDir}/checkstyle.xml</configLocation>
          <suppressionsLocation>${log4jParentDir}/checkstyle-suppressions.xml</suppressionsLocation>
          <enableRulesSummary>false</enableRulesSummary>
          <propertyExpansion>basedir=${basedir}</propertyExpansion>
          <propertyExpansion>licensedir=${log4jParentDir}/checkstyle-header.txt</propertyExpansion>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${javadoc.plugin.version}</version>
        <configuration>
          <bottom><![CDATA[<p align="center">Copyright &#169; {inceptionYear}-{currentYear} {organizationName}. All Rights Reserved.<br />
            Apache Logging, Apache Log4j, Log4j, Apache, the Apache feather logo, the Apache Logging project logo,
            and the Apache Log4j logo are trademarks of The Apache Software Foundation.</p>]]></bottom>
          <!-- module link generation is completely broken in the javadoc plugin for a multi-module non-aggregating
               project -->
          <detectOfflineLinks>false</detectOfflineLinks>
          <linksource>true</linksource>
        </configuration>
        <reportSets>
          <reportSet>
            <id>non-aggregate</id>
            <reports>
              <report>javadoc</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${jxr.plugin.version}</version>
        <reportSets>
          <reportSet>
            <id>non-aggregate</id>
            <reports>
              <report>jxr</report>
            </reports>
          </reportSet>
          <reportSet>
            <id>aggregate</id>
            <reports>
              <report>aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${pmd.plugin.version}</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
        </configuration>
      </plugin>
    </plugins>
  </reporting>
</project>

