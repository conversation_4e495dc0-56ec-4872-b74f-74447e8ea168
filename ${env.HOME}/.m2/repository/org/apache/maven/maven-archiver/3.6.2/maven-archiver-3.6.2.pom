<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at
  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.shared</groupId>
    <artifactId>maven-shared-components</artifactId>
    <version>41</version>
    <relativePath />
  </parent>

  <groupId>org.apache.maven</groupId>
  <artifactId>maven-archiver</artifactId>
  <version>3.6.2</version>

  <name>Apache Maven Archiver</name>
  <description>Provides utility methods for creating JARs and other archive files from a Maven project.</description>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-archiver.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-archiver.git</developerConnection>
    <tag>maven-archiver-3.6.2</tag>
    <url>https://github.com/apache/maven-archiver/tree/${project.scm.tag}</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/issues/?jql=project%3DMSHARED+AND+component%3Dmaven-archiver</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-maven.apache.org/job/Maven/job/maven-box/job/maven-archiver/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <mavenVersion>3.2.5</mavenVersion>
    <javaVersion>8</javaVersion>
    <project.build.outputTimestamp>2024-03-24T22:30:44Z</project.build.outputTimestamp>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>5.10.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!--
      Apache Maven dependencies
    -->
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <!--
      Plexus dependencies
    -->
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-archiver</artifactId>
      <version>4.9.2</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-interpolation</artifactId>
      <version>1.27</version>
    </dependency>
    <!--
      Test dependencies
    -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.25.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.11.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>1.7.36</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
