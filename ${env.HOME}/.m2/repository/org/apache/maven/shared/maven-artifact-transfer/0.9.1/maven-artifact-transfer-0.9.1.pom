<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>maven-shared-components</artifactId>
    <groupId>org.apache.maven.shared</groupId>
    <version>30</version>
    <relativePath>../../pom/maven/maven-shared-components/pom.xml/pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>maven-artifact-transfer</artifactId>
  <name>Apache Maven Artifact Transfer</name>
  <version>0.9.1</version>
  <description>An API to install, deploy and resolving artifacts with Maven 3</description>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/MSHARED/component/12327114</url>
  </issueManagement>
  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/shared/tags/maven-artifact-transfer-0.9.1</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/shared/tags/maven-artifact-transfer-0.9.1</developerConnection>
    <url>http://svn.apache.org/viewvc/maven/shared/tags/maven-artifact-transfer-0.9.1</url>
  </scm>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <version>1.7.1</version>
        <executions>
          <execution>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>2.4.3</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <includes>
                  <include>org.eclipse.aether:aether-util</include>
                </includes>
              </artifactSet>
              <filters>
                <filter>
                  <artifact>org.eclipse.aether:aether-util</artifact>
                  <includes>
                    <include>org/eclipse/aether/util/artifact/SubArtifact.class</include>
                    <include>org/eclipse/aether/util/filter/*</include>
                  </includes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>dependency-reduced-pom.xml</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.15</version>
        <executions>
          <execution>
            <id>sniff</id>
            <phase>test</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java16</artifactId>
            <version>1.1</version>
          </signature>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-bytecode-version</id>
            <configuration>
              <rules>
                <enforceBytecodeVersion>
                  <maxJdkVersion>1.6</maxJdkVersion>
                </enforceBytecodeVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <pluginRepositories>
    <pluginRepository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots />
      <id>plexus-snapshots</id>
      <url>https://oss.sonatype.org/content/repositories/plexus-snapshots</url>
    </pluginRepository>
  </pluginRepositories>
  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>3.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>3.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
      <version>1.7.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-common-artifact-filters</artifactId>
      <version>3.0.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.0.24</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.sonatype.aether</groupId>
      <artifactId>aether-api</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.sonatype.aether</groupId>
      <artifactId>aether-util</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.sonatype.aether</groupId>
      <artifactId>aether-impl</artifactId>
      <version>1.7</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>aether-spi</artifactId>
          <groupId>org.sonatype.aether</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.aether</groupId>
      <artifactId>aether-api</artifactId>
      <version>0.9.0.M2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.aether</groupId>
      <artifactId>aether-impl</artifactId>
      <version>0.9.0.M2</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>aether-spi</artifactId>
          <groupId>org.eclipse.aether</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/components/${maven.site.path}</url>
    </site>
  </distributionManagement>
</project>

