<?xml version='1.0' encoding='UTF-8'?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-plugins</artifactId>
    <version>24</version>
    <relativePath>../maven-plugins/pom.xml</relativePath>
  </parent>

  <artifactId>maven-compiler-plugin</artifactId>
  <version>3.1</version>
  <packaging>maven-plugin</packaging>

  <name>Maven Compiler Plugin</name>
  <description>The Compiler Plugin is used to compile the sources of your project.</description>
  <inceptionYear>2001</inceptionYear>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/plugins/tags/maven-compiler-plugin-3.1</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/plugins/tags/maven-compiler-plugin-3.1</developerConnection>
    <url>http://svn.apache.org/viewvc/maven/plugins/tags/maven-compiler-plugin-3.1</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>http://jira.codehaus.org/browse/MCOMPILER</url>
  </issueManagement>

  <properties>
    <mavenVersion>2.0.9</mavenVersion>
    <mavenPluginPluginVersion>3.2</mavenPluginPluginVersion>
    <plexusCompilerVersion>2.2</plexusCompilerVersion>
    <groovyVersion>1.8.0</groovyVersion>
    <groovyEclipseCompilerVersion>2.7.0-01</groovyEclipseCompilerVersion>
    <groovy-eclipse-batch>2.0.4-04</groovy-eclipse-batch>
    <openJpaVersion>2.2.0</openJpaVersion>
  </properties>

  <contributors>
    <contributor>
      <name>Jan Sievers</name>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
      <version>${mavenPluginPluginVersion}</version>
      <scope>provided</scope>
    </dependency>
    <!-- Maven -->
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.maven.reporting</groupId>
          <artifactId>maven-reporting-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven.wagon</groupId>
          <artifactId>wagon-file</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven.wagon</groupId>
          <artifactId>wagon-http-lightweight</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven.wagon</groupId>
          <artifactId>wagon-ssh</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven.wagon</groupId>
          <artifactId>wagon-ssh-external</artifactId>
        </exclusion>
        <exclusion>
          <groupId>commons-cli</groupId>
          <artifactId>commons-cli</artifactId>
        </exclusion>
        <exclusion>
          <groupId>classworlds</groupId>
          <artifactId>classworlds</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-container-default</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-interactivity-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-toolchain</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-shared-utils</artifactId>
      <version>0.1</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-shared-incremental</artifactId>
      <version>1.1</version>
    </dependency>

    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-compiler-api</artifactId>
      <version>${plexusCompilerVersion}</version>
      <exclusions>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-compiler-manager</artifactId>
      <version>${plexusCompilerVersion}</version>
      <exclusions>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-compiler-javac</artifactId>
      <version>${plexusCompilerVersion}</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-container-default</artifactId>
      <version>1.5.5</version>
    </dependency>

    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>2.0-alpha-1</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.8.1</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${mavenPluginPluginVersion}</version>
          <configuration>
            <skipErrorNoDescriptorsFound>true</skipErrorNoDescriptorsFound>
          </configuration>
          <executions>
            <execution>
              <id>mojo-descriptor</id>
              <phase>process-classes</phase>
              <goals>
                <goal>descriptor</goal>
              </goals>
            </execution>
            <execution>
              <id>help-goal</id>
              <goals>
                <goal>helpmojo</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
        <version>${mavenPluginPluginVersion}</version>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-invoker-plugin</artifactId>
              <configuration>
                <debug>true</debug>
                <projectsDirectory>src/it</projectsDirectory>
                <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
                <pomIncludes>
                  <pomInclude>*/pom.xml</pomInclude>
                  <pomInclude>extras/*/pom.xml</pomInclude>
                </pomIncludes>
                <postBuildHookScript>verify</postBuildHookScript>
                <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
                <settingsFile>src/it/settings.xml</settingsFile>
                <goals>
                  <goal>clean</goal>
                  <goal>test-compile</goal>
                </goals>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
      <!--
      add those dependency just to have faster it test (for folks who doesn't have those locally
      they will be downloaded from local repo rather than central
      -->
      <dependencies>
        <dependency>
          <groupId>org.codehaus.groovy</groupId>
          <artifactId>groovy-eclipse-compiler</artifactId>
          <version>${groovyEclipseCompilerVersion}</version>
          <scope>test</scope>
        </dependency>
        <dependency>
          <groupId>org.codehaus.groovy</groupId>
          <artifactId>groovy-eclipse-batch</artifactId>
          <version>${groovy-eclipse-batch}</version>
          <scope>test</scope>
        </dependency>
        <dependency>
          <groupId>org.codehaus.groovy</groupId>
          <artifactId>groovy-all</artifactId>
          <version>${groovyVersion}</version>
          <scope>test</scope>
        </dependency>
        <dependency>
          <groupId>org.apache.openjpa</groupId>
          <artifactId>openjpa</artifactId>
          <version>${openJpaVersion}</version>
        </dependency>
      </dependencies>
    </profile>
  </profiles>

</project>
