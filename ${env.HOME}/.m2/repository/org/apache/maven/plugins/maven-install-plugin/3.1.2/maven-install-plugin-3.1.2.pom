<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements. See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership. The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License. You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied. See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-plugins</artifactId>
    <version>42</version>
    <relativePath />
  </parent>

  <artifactId>maven-install-plugin</artifactId>
  <version>3.1.2</version>
  <packaging>maven-plugin</packaging>

  <name>Apache Maven Install Plugin</name>
  <description>Copies the project artifacts to the user's local repository.</description>
  <inceptionYear>2004</inceptionYear>

  <contributors>
    <!-- alphabetic order -->
    <contributor>
      <name>Hermann Josef Hill</name>
    </contributor>
    <contributor>
      <name>Ludwig Magnusson</name>
    </contributor>
  </contributors>

  <prerequisites>
    <maven>3.6.3</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-install-plugin.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-install-plugin.git</developerConnection>
    <tag>maven-install-plugin-3.1.2</tag>
    <url>https://github.com/apache/maven-install-plugin/tree/${project.scm.tag}</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/MINSTALL</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-maven.apache.org/job/Maven/job/maven-box/job/maven-install-plugin/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <javaVersion>8</javaVersion>
    <mavenVersion>3.9.6</mavenVersion>
    <!-- Maven bound -->
    <resolverVersion>1.9.18</resolverVersion>
    <!-- Maven bound -->
    <slf4jVersion>1.7.36</slf4jVersion>

    <!-- plugin versions used in IT tests -->
    <mavenAntrunPluginVersion>${version.maven-antrun-plugin}</mavenAntrunPluginVersion>
    <mavenCompilerPluginVersion>${version.maven-compiler-plugin}</mavenCompilerPluginVersion>
    <mavenEnforcerPluginVersion>${version.maven-enforcer-plugin}</mavenEnforcerPluginVersion>
    <mavenJarPluginVersion>${version.maven-jar-plugin}</mavenJarPluginVersion>
    <mavenPluginPluginVersion>${version.maven-plugin-tools}</mavenPluginPluginVersion>
    <mavenResourcesPluginVersion>${version.maven-resources-plugin}</mavenResourcesPluginVersion>
    <mavenSourcePluginVersion>${version.maven-source-plugin}</mavenSourcePluginVersion>
    <mavenSurefirePluginVersion>${version.maven-surefire}</mavenSurefirePluginVersion>

    <project.build.outputTimestamp>2024-04-26T10:22:49Z</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <!-- This is here to override 3.0 coming with maven-artifact-transfer -->
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-model</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.resolver</groupId>
      <artifactId>maven-resolver-api</artifactId>
      <version>${resolverVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.resolver</groupId>
      <artifactId>maven-resolver-util</artifactId>
      <version>${resolverVersion}</version>
      <!-- To work in Maven versions older than 3.9.0 -->
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-xml</artifactId>
    </dependency>

    <!-- dependencies to annotations -->
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>3.3.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <!-- used by maven-plugin-testing-harness, don't give it compile scope! -->
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-compat</artifactId>
      <version>${mavenVersion}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.8.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4jVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-nop</artifactId>
      <version>${slf4jVersion}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.resolver</groupId>
      <artifactId>maven-resolver-impl</artifactId>
      <version>${resolverVersion}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <proc>none</proc>
          <showDeprecation>true</showDeprecation>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-invoker-plugin</artifactId>
              <configuration>
                <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
                <cloneClean>true</cloneClean>
                <pomIncludes>
                  <pomInclude>*/pom.xml</pomInclude>
                  <pomInclude>*/non-default-pom.xml</pomInclude>
                </pomIncludes>
                <preBuildHookScript>setup</preBuildHookScript>
                <postBuildHookScript>verify</postBuildHookScript>
                <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
                <settingsFile>src/it/settings.xml</settingsFile>
                <addTestClassPath>true</addTestClassPath>
                <goals>
                  <goal>clean</goal>
                  <goal>install</goal>
                </goals>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>

</project>
