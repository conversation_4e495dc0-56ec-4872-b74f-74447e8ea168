<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.flink</groupId>
    <artifactId>flink-shaded</artifactId>
    <version>17.0</version>
  </parent>
  <groupId>org.apache.flink</groupId>
  <artifactId>flink-shaded-netty</artifactId>
  <version>4.1.91.Final-17.0</version>
  <name>flink-shaded-netty-4</name>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <id>shade-flink</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>true</createDependencyReducedPom>
              <dependencyReducedPomLocation>${project.basedir}/target/dependency-reduced-pom.xml</dependencyReducedPomLocation>
              <artifactSet>
                <includes>
                  <include>io.netty:*</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>io.netty</pattern>
                  <shadedPattern>${shading.prefix}.netty4.io.netty</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>1.7</version>
        <executions>
          <execution>
            <id>rename-native-library</id>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <echo message="unpacking netty jar" />
                <unzip src="${project.build.directory}/${artifactId}-${version}.jar" dest="${project.build.directory}/unpacked/" />
                <echo message="renaming the complete libnetty library" />
                <move includeemptydirs="false" todir="${project.build.directory}/unpacked/META-INF/native">
                  <fileset dir="${project.build.directory}/unpacked/META-INF/native" />
                  <mapper from="libnetty*" to="liborg_apache_flink_shaded_netty4_netty*" type="glob" />
                </move>
                <echo message="repackaging netty jar" />
                <jar basedir="${project.build.directory}/unpacked" destfile="${project.build.directory}/${artifactId}-${version}.jar" />
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>license-check</id>
      <properties>
        <flink.ci.license.suffix>-4</flink.ci.license.suffix>
      </properties>
    </profile>
  </profiles>
</project>
