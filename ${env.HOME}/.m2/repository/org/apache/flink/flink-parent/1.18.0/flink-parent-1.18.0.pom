<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>apache</artifactId>
    <groupId>org.apache</groupId>
    <version>20</version>
    <relativePath>../pom.xml/pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.flink</groupId>
  <artifactId>flink-parent</artifactId>
  <packaging>pom</packaging>
  <name>Flink :</name>
  <version>1.18.0</version>
  <url>https://flink.apache.org</url>
  <inceptionYear>2014</inceptionYear>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <connection>**************:apache/flink.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/flink.git</developerConnection>
    <url>https://github.com/apache/flink</url>
  </scm>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.0</version>
          <configuration>
            <source>${target.java.version}</source>
            <target>${target.java.version}</target>
            <useIncrementalCompilation>false</useIncrementalCompilation>
            <compilerArgs>
              <arg>-Xpkginfo:always</arg>
            </compilerArgs>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.1.0</version>
          <configuration>
            <filesets>
              <fileset>
                <directory>${project.basedir}</directory>
                <includes>
                  <include>dependency-reduced-pom.xml</include>
                </includes>
              </fileset>
            </filesets>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>3.1.2</version>
          <executions>
            <execution>
              <id>validate</id>
              <phase>validate</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
          <dependencies>
            <dependency>
              <groupId>com.puppycrawl.tools</groupId>
              <artifactId>checkstyle</artifactId>
              <version>8.14</version>
            </dependency>
          </dependencies>
          <configuration>
            <suppressionsLocation>/tools/maven/suppressions.xml</suppressionsLocation>
            <includeTestSourceDirectory>true</includeTestSourceDirectory>
            <configLocation>/tools/maven/checkstyle.xml</configLocation>
            <logViolationsToConsole>true</logViolationsToConsole>
            <failOnViolation>true</failOnViolation>
          </configuration>
        </plugin>
        <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <version>${spotless.version}</version>
          <executions>
            <execution>
              <id>spotless-check</id>
              <phase>validate</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <java>
              <googleJavaFormat>
                <version>1.7</version>
                <style>AOSP</style>
              </googleJavaFormat>
              <importOrder>
                <order>org.apache.flink,org.apache.flink.shaded,,javax,java,scala,\#</order>
              </importOrder>
              <removeUnusedImports />
            </java>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.9.1</version>
          <configuration>
            <quiet>true</quiet>
            <detectOfflineLinks>false</detectOfflineLinks>
            <additionalJOptions>
              <additionalJOption>-Xdoclint:none</additionalJOption>
            </additionalJOptions>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.2.0</version>
          <configuration>
            <ignoredUsedUndeclaredDependencies>
              <dependency>org.apache.flink:*</dependency>
              <dependency>org.hamcrest:hamcrest-core</dependency>
              <dependency>org.powermock:powermock-core</dependency>
              <dependency>org.powermock:powermock-reflect</dependency>
              <dependency>org.powermock:powermock-api-support</dependency>
            </ignoredUsedUndeclaredDependencies>
            <ignoredUnusedDeclaredDependencies>
              <dependency>org.apache.flink:force-shading</dependency>
              <dependency>com.google.code.findbugs:jsr305</dependency>
              <dependency>org.scala-lang:scala-compiler</dependency>
              <dependency>org.slf4j:slf4j-api</dependency>
              <dependency>log4j:log4j</dependency>
              <dependency>org.slf4j:slf4j-log4j12</dependency>
              <dependency>org.apache.logging.log4j:log4j-slf4j-impl</dependency>
              <dependency>org.apache.logging.log4j:log4j-api</dependency>
              <dependency>org.apache.logging.log4j:log4j-core</dependency>
              <dependency>org.apache.logging.log4j:log4j-1.2-api</dependency>
              <dependency>org.apache.flink:flink-test-utils-junit</dependency>
              <dependency>junit:junit</dependency>
              <dependency>org.mockito:mockito-core</dependency>
              <dependency>org.powermock:powermock-api-mockito2</dependency>
              <dependency>org.powermock:powermock-module-junit4</dependency>
              <dependency>org.hamcrest:hamcrest-all</dependency>
            </ignoredUnusedDeclaredDependencies>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-shade-plugin</artifactId>
          <version>3.4.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <executions>
            <execution>
              <id>attach-descriptor</id>
              <phase>none</phase>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>net.alchim31.maven</groupId>
          <artifactId>scala-maven-plugin</artifactId>
          <version>3.2.2</version>
          <configuration>
            <args>
              <arg>-nobootcp</arg>
              <arg>-target:jvm-${target.java.version}</arg>
            </args>
            <jvmArgs>
              <arg>-Xss2m</arg>
            </jvmArgs>
          </configuration>
        </plugin>
        <plugin>
          <groupId>io.github.zentol.japicmp</groupId>
          <artifactId>japicmp-maven-plugin</artifactId>
          <version>0.17.1.1_m325</version>
          <executions>
            <execution>
              <phase>verify</phase>
              <goals>
                <goal>cmp</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <oldVersion>
              <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>${project.artifactId}</artifactId>
                <version>${japicmp.referenceVersion}</version>
                <type>${project.packaging}</type>
              </dependency>
            </oldVersion>
            <newVersion>
              <file>
                <path>${project.build.directory}/${project.artifactId}-${project.version}.${project.packaging}</path>
              </file>
            </newVersion>
            <parameter>
              <onlyModified>true</onlyModified>
              <includes>
                <include>@org.apache.flink.annotation.Public</include>
              </includes>
              <excludes>
                <exclude>@org.apache.flink.annotation.Experimental</exclude>
                <exclude>@org.apache.flink.annotation.PublicEvolving</exclude>
                <exclude>@org.apache.flink.annotation.Internal</exclude>
              </excludes>
              <accessModifier>public</accessModifier>
              <breakBuildOnModifications>false</breakBuildOnModifications>
              <breakBuildOnBinaryIncompatibleModifications>true</breakBuildOnBinaryIncompatibleModifications>
              <breakBuildOnSourceIncompatibleModifications>true</breakBuildOnSourceIncompatibleModifications>
              <onlyBinaryIncompatible>false</onlyBinaryIncompatible>
              <includeSynthetic>true</includeSynthetic>
              <ignoreMissingClasses>false</ignoreMissingClasses>
              <skipPomModules>true</skipPomModules>
              <ignoreNonResolvableArtifacts>true</ignoreNonResolvableArtifacts>
              <packagingSupporteds>
                <packagingSupported>jar</packagingSupported>
              </packagingSupporteds>
            </parameter>
            <projectBuildDir>${rootDir}/${japicmp.outputDir}/${project.artifactId}</projectBuildDir>
            <dependencies>
              <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-annotations</artifactId>
                <version>${project.version}</version>
              </dependency>
            </dependencies>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.owasp</groupId>
          <artifactId>dependency-check-maven</artifactId>
          <version>5.0.0-M2</version>
          <configuration>
            <format>ALL</format>
            <skipSystemScope>true</skipSystemScope>
            <skipProvidedScope>true</skipProvidedScope>
            <excludes>
              <exclude>*flink-docs</exclude>
              <exclude>*flink-end-to-end-tests</exclude>
              <exclude>*flink-fs-tests*</exclude>
              <exclude>*flink-yarn-tests*</exclude>
            </excludes>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>3.0.1</version>
        <extensions>true</extensions>
        <inherited>true</inherited>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <archive>
            <addMavenDescriptor>false</addMavenDescriptor>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>io.github.zentol.japicmp</groupId>
        <artifactId>japicmp-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>0.13</version>
        <executions>
          <execution>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <inherited>false</inherited>
        <configuration>
          <consoleOutput>true</consoleOutput>
          <excludeSubProjects>false</excludeSubProjects>
          <numUnapprovedLicenses>0</numUnapprovedLicenses>
          <licenses>
            <license>
              <licenseFamilyCategory>AL2</licenseFamilyCategory>
              <licenseFamilyName>Apache License 2.0</licenseFamilyName>
              <notes />
              <patterns>
                <pattern>Licensed to the Apache Software Foundation (ASF) under one</pattern>
              </patterns>
            </license>
          </licenses>
          <licenseFamilies>
            <licenseFamily>
              <familyName>Apache License 2.0</familyName>
            </licenseFamily>
          </licenseFamilies>
          <excludes>
            <exclude>**/.*/**</exclude>
            <exclude>**/*.prefs</exclude>
            <exclude>**/*.log</exclude>
            <exclude>docs/**/jquery*</exclude>
            <exclude>docs/**/bootstrap*</exclude>
            <exclude>docs/themes/book/**</exclude>
            <exclude>docs/**/anchor*</exclude>
            <exclude>**/resources/**/font-awesome/**</exclude>
            <exclude>**/resources/**/jquery*</exclude>
            <exclude>**/resources/**/bootstrap*</exclude>
            <exclude>docs/resources/**</exclude>
            <exclude>docs/public/**</exclude>
            <exclude>docs/assets/github.css</exclude>
            <exclude>docs/static/flink-header-logo.svg</exclude>
            <exclude>docs/static/figs/*.svg</exclude>
            <exclude>docs/static/font-awesome/**</exclude>
            <exclude>docs/static/flink-header-logo.svg</exclude>
            <exclude>docs/static/figs/*.svg</exclude>
            <exclude>flink-clients/src/main/resources/web-docs/js/*d3.js</exclude>
            <exclude>**/packaged_licenses/LICENSE.*.txt</exclude>
            <exclude>**/licenses/LICENSE*</exclude>
            <exclude>**/licenses-binary/LICENSE*</exclude>
            <exclude>flink-runtime-web/web-dashboard/package.json</exclude>
            <exclude>flink-runtime-web/web-dashboard/package-lock.json</exclude>
            <exclude>flink-runtime-web/web-dashboard/angular.json</exclude>
            <exclude>flink-runtime-web/web-dashboard/proxy.conf.json</exclude>
            <exclude>flink-runtime-web/web-dashboard/tsconfig.json</exclude>
            <exclude>flink-runtime-web/web-dashboard/tslint.json</exclude>
            <exclude>flink-runtime-web/web-dashboard/src/browserslist</exclude>
            <exclude>flink-runtime-web/web-dashboard/src/tsconfig.app.json</exclude>
            <exclude>flink-runtime-web/web-dashboard/src/tsconfig.spec.json</exclude>
            <exclude>flink-runtime-web/web-dashboard/src/tslint.json</exclude>
            <exclude>flink-runtime-web/web-dashboard/src/assets/**</exclude>
            <exclude>flink-runtime-web/web-dashboard/web/**</exclude>
            <exclude>flink-runtime-web/web-dashboard/node_modules/**</exclude>
            <exclude>flink-runtime-web/web-dashboard/node/**</exclude>
            <exclude>flink-table/flink-table-code-splitter/src/main/antlr4/**</exclude>
            <exclude>**/src/test/resources/*-data</exclude>
            <exclude>flink-tests/src/test/resources/testdata/terainput.txt</exclude>
            <exclude>flink-formats/flink-avro/src/test/resources/flink_11-kryo_registrations</exclude>
            <exclude>flink-scala/src/test/resources/flink_11-kryo_registrations</exclude>
            <exclude>flink-core/src/test/resources/kryo-serializer-config-snapshot-v1</exclude>
            <exclude>flink-core/src/test/resources/abstractID-with-toString-field</exclude>
            <exclude>flink-core/src/test/resources/abstractID-with-toString-field-set</exclude>
            <exclude>flink-formats/flink-avro/src/test/resources/avro/*.avsc</exclude>
            <exclude>out/test/flink-avro/avro/user.avsc</exclude>
            <exclude>flink-table/flink-sql-client/src/test/resources/**/*.out</exclude>
            <exclude>flink-table/flink-table-planner/src/test/resources/**/*.out</exclude>
            <exclude>flink-table/flink-table-planner/src/test/resources/json/*.json</exclude>
            <exclude>flink-yarn/src/test/resources/krb5.keytab</exclude>
            <exclude>flink-end-to-end-tests/test-scripts/test-data/**</exclude>
            <exclude>flink-end-to-end-tests/test-scripts/docker-hadoop-secure-cluster/hadoop/config/keystore.jks</exclude>
            <exclude>flink-connectors/flink-connector-hive/src/test/resources/**</exclude>
            <exclude>flink-connectors/flink-file-sink-common/src/test/resources/recoverable-serializer-migration/**</exclude>
            <exclude>flink-end-to-end-tests/flink-tpcds-test/tpcds-tool/answer_set/*</exclude>
            <exclude>flink-end-to-end-tests/flink-tpcds-test/tpcds-tool/query/*</exclude>
            <exclude>flink-table/flink-table-code-splitter/src/test/resources/**</exclude>
            <exclude>**/archunit-violations/**</exclude>
            <exclude>**/src/test/resources/serializer-snapshot-*</exclude>
            <exclude>**/src/test/resources/**/serializer-snapshot</exclude>
            <exclude>**/src/test/resources/**/test-data</exclude>
            <exclude>**/src/test/resources/*-snapshot</exclude>
            <exclude>**/src/test/resources/*.snapshot</exclude>
            <exclude>**/src/test/resources/*-savepoint/**</exclude>
            <exclude>**/src/test/resources/*-savepoint-native/**</exclude>
            <exclude>**/src/test/resources/*-checkpoint/**</exclude>
            <exclude>flink-core/src/test/resources/serialized-kryo-serializer-1.3</exclude>
            <exclude>flink-core/src/test/resources/type-without-avro-serialized-using-kryo</exclude>
            <exclude>flink-formats/flink-avro/src/test/resources/flink-1.4-serializer-java-serialized</exclude>
            <exclude>flink-end-to-end-tests/flink-state-evolution-test/src/main/java/org/apache/flink/avro/generated/*</exclude>
            <exclude>flink-end-to-end-tests/flink-state-evolution-test/savepoints/*</exclude>
            <exclude>flink-formats/flink-avro/src/test/resources/testdata.avro</exclude>
            <exclude>flink-formats/flink-avro/src/test/java/org/apache/flink/formats/avro/generated/*.java</exclude>
            <exclude>flink-formats/flink-avro-confluent-registry/src/test/resources/*.json</exclude>
            <exclude>flink-formats/flink-avro-confluent-registry/src/test/resources/*.avro</exclude>
            <exclude>flink-formats/flink-json/src/test/resources/*.txt</exclude>
            <exclude>flink-formats/flink-parquet/src/test/java/org/apache/flink/formats/parquet/generated/*.java</exclude>
            <exclude>flink-formats/flink-parquet/src/test/resources/avro/**</exclude>
            <exclude>flink-formats/flink-parquet/src/test/resources/protobuf/**</exclude>
            <exclude>flink-table/flink-sql-gateway/src/test/resources/*.txt</exclude>
            <exclude>flink-runtime/src/test/java/org/apache/flink/runtime/io/network/buffer/AbstractByteBufTest.java</exclude>
            <exclude>**/flink-bin/conf/workers</exclude>
            <exclude>**/flink-bin/conf/masters</exclude>
            <exclude>**/README.md</exclude>
            <exclude>.github/**</exclude>
            <exclude>**/*.iml</exclude>
            <exclude>flink-quickstart/**/testArtifact/goal.txt</exclude>
            <exclude>out/**</exclude>
            <exclude>**/target/**</exclude>
            <exclude>build-target/**</exclude>
            <exclude>docs/layouts/shortcodes/generated/**</exclude>
            <exclude>docs/themes/connectors/layouts/shortcodes/generated/**</exclude>
            <exclude>docs/static/generated/**</exclude>
            <exclude>tools/artifacts/**</exclude>
            <exclude>tools/flink*/**</exclude>
            <exclude>tools/japicmp-output/**</exclude>
            <exclude>tools/releasing/release/**</exclude>
            <exclude>**/.idea/**</exclude>
            <exclude>flink-end-to-end-tests/flink-confluent-schema-registry/src/main/java/example/avro/**</exclude>
            <exclude>flink-end-to-end-tests/flink-datastream-allround-test/src/main/java/org/apache/flink/streaming/tests/avro/**</exclude>
            <exclude>flink-python/lib/**</exclude>
            <exclude>flink-python/dev/download/**</exclude>
            <exclude>flink-python/docs/_build/**</exclude>
            <exclude>flink-python/docs/_static/switcher.json</exclude>
            <exclude>**/awssdk/global/handlers/execution.interceptors</exclude>
            <exclude>flink-test-utils-parent/flink-migration-test-utils/src/main/resources/most_recently_published_version</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0-M5</version>
        <executions>
          <execution>
            <id>default-test</id>
            <phase>test</phase>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <includes>
                <include>${test.unit.pattern}</include>
              </includes>
              <forkCount>${flink.forkCountUnitTest}</forkCount>
              <argLine>${flink.surefire.baseArgLine} -Xmx${flink.XmxUnitTest}</argLine>
            </configuration>
          </execution>
          <execution>
            <id>integration-tests</id>
            <phase>integration-test</phase>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <includes>
                <include>**/*.*</include>
              </includes>
              <excludes>
                <exclude>${test.unit.pattern}</exclude>
                <exclude>**/*$*</exclude>
              </excludes>
              <forkCount>${flink.forkCountITCase}</forkCount>
              <argLine>${flink.surefire.baseArgLine} -Xmx${flink.XmxITCase}</argLine>
              <reuseForks>false</reuseForks>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <forkNode />
          <reuseForks>${flink.reuseForks}</reuseForks>
          <trimStackTrace>false</trimStackTrace>
          <systemPropertyVariables>
            <forkNumber>0${surefire.forkNumber}</forkNumber>
            <mvn.forkNumber>$${surefire.forkNumber}</mvn.forkNumber>
            <hadoop.version>${flink.hadoop.version}</hadoop.version>
            <checkpointing.randomization>true</checkpointing.randomization>
            <buffer-debloat.randomization>true</buffer-debloat.randomization>
            <user.country>US</user.country>
            <user.language>en</user.language>
            <checkpointing.changelog>random</checkpointing.changelog>
            <surefire.module.config>${surefire.module.config}</surefire.module.config>
            <project.basedir>${project.basedir}</project.basedir>
            <test.randomization.seed>${test.randomization.seed}</test.randomization.seed>
            <junit.jupiter.extensions.autodetection.enabled>true</junit.jupiter.extensions.autodetection.enabled>
            <junit.jupiter.execution.parallel.enabled>true</junit.jupiter.execution.parallel.enabled>
            <junit.jupiter.execution.parallel.mode.default>same_thread</junit.jupiter.execution.parallel.mode.default>
            <junit.jupiter.execution.parallel.mode.classes.default>same_thread</junit.jupiter.execution.parallel.mode.classes.default>
            <junit.jupiter.execution.parallel.config.strategy>dynamic</junit.jupiter.execution.parallel.config.strategy>
          </systemPropertyVariables>
          <argLine>${flink.surefire.baseArgLine}</argLine>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-eclipse-plugin</artifactId>
        <version>2.8</version>
        <configuration>
          <classpathContainers>
            <classpathContainer>org.eclipse.jdt.launching.JRE_CONTAINER</classpathContainer>
          </classpathContainers>
          <downloadSources>true</downloadSources>
          <downloadJavadocs>true</downloadJavadocs>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-maven</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>[3.1.1,)</version>
                </requireMavenVersion>
                <requireJavaVersion>
                  <version>${target.java.version}</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
          <execution>
            <id>ban-unsafe-snakeyaml</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>org.yaml:snakeyaml:(,1.31]</exclude>
                  </excludes>
                  <includes>
                    <include>org.yaml:snakeyaml:(,1.31]:*:test</include>
                  </includes>
                  <message>Older snakeyaml versions are not allowed due to security vulnerabilities.</message>
                </bannedDependencies>
              </rules>
            </configuration>
          </execution>
          <execution>
            <id>ban-unsafe-jackson</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>com.fasterxml.jackson*:*:(,2.12.0]</exclude>
                  </excludes>
                  <message>Older jackson versions are not allowed due to security vulnerabilities.</message>
                </bannedDependencies>
              </rules>
            </configuration>
          </execution>
          <execution>
            <id>forbid-log4j-1</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>log4j:log4j</exclude>
                    <exclude>org.slf4j:slf4j-log4j12</exclude>
                    <exclude>ch.qos.reload4j:reload4j</exclude>
                    <exclude>org.slf4j:slf4j-reload4j</exclude>
                  </excludes>
                  <message>Log4j 1 and Reload4J dependencies are not allowed because they conflict with Log4j 2. If the dependency absolutely requires the Log4j 1 API, use 'org.apache.logging.log4j:log4j-1.2-api'.</message>
                </bannedDependencies>
              </rules>
            </configuration>
          </execution>
          <execution>
            <id>forbid-direct-akka-rpc-dependencies</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>org.apache.flink:flink-rpc-akka</exclude>
                  </excludes>
                  <message>Direct dependencies on flink-rpc-akka are not allowed. Depend on flink-rpc-akka-loader instead, and use RpcSystem#load or the TestingRpcService.</message>
                </bannedDependencies>
              </rules>
            </configuration>
          </execution>
          <execution>
            <id>forbid-direct-table-planner-dependencies</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>org.apache.flink:flink-table-planner_${scala.binary.version}</exclude>
                  </excludes>
                  <includes>
                    <include>org.apache.flink:flink-table-planner_${scala.binary.version}:*:*:test</include>
                  </includes>
                  <message>Direct dependencies on flink-table-planner are not allowed.
										You should depend on either Table API modules or flink-table-planner-loader.</message>
                </bannedDependencies>
              </rules>
            </configuration>
          </execution>
          <execution>
            <id>dependency-convergence</id>
            <phase>none</phase>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <dependencyConvergence />
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <id>shade-flink</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <shadeTestJar>false</shadeTestJar>
              <shadedArtifactAttached>false</shadedArtifactAttached>
              <createDependencyReducedPom>true</createDependencyReducedPom>
              <filters>
                <filter>
                  <artifact>org.apache.flink:flink-shaded-force-shading</artifact>
                  <excludes>
                    <exclude>**</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>io.netty:netty</artifact>
                  <excludes>
                    <exclude>META-INF/LICENSE.txt</exclude>
                  </excludes>
                </filter>
              </filters>
              <artifactSet>
                <includes>
                  <include>org.apache.flink:flink-shaded-force-shading</include>
                </includes>
              </artifactSet>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <filters>
            <filter>
              <artifact>*</artifact>
              <excludes>
                <exclude>log4j.properties</exclude>
                <exclude>log4j2.properties</exclude>
                <exclude>log4j-test.properties</exclude>
                <exclude>log4j2-test.properties</exclude>
                <exclude>META-INF/*.SF</exclude>
                <exclude>META-INF/*.DSA</exclude>
                <exclude>META-INF/*.RSA</exclude>
                <exclude>**/META-INF/maven/?*/?*/**</exclude>
              </excludes>
            </filter>
          </filters>
          <transformers>
            <transformer />
            <transformer>
              <projectName>Apache Flink</projectName>
              <encoding>UTF-8</encoding>
            </transformer>
          </transformers>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.commonjava.maven.plugins</groupId>
        <artifactId>directory-maven-plugin</artifactId>
        <version>0.1</version>
        <executions>
          <execution>
            <id>directories</id>
            <phase>initialize</phase>
            <goals>
              <goal>directory-of</goal>
            </goals>
            <configuration>
              <property>rootDir</property>
              <project>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-parent</artifactId>
              </project>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.cyclonedx</groupId>
        <artifactId>cyclonedx-maven-plugin</artifactId>
        <version>2.7.7</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>makeBom</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>intellij</id>
      <properties>
        <flink.markBundledAsOptional>false</flink.markBundledAsOptional>
      </properties>
    </profile>
    <profile>
      <id>scala-2.12</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-versions</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <bannedDependencies>
                      <excludes>
                        <exclude>*:*_2.11</exclude>
                        <exclude>*:*_2.10</exclude>
                      </excludes>
                      <message>Scala 2.10/2.11 dependencies are not allowed for Scala 2.12 builds. This can be caused by hard-coded scala versions, where the 'scala.binary.version' property should be used instead.</message>
                    </bannedDependencies>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <properties>
        <os.detected.bitness>64</os.detected.bitness>
        <scala.version>2.12.7</scala.version>
        <os.detected.name>osx</os.detected.name>
        <scala.binary.version>2.12</scala.binary.version>
        <os.detected.classifier>osx-aarch_64</os.detected.classifier>
        <os.detected.arch>aarch_64</os.detected.arch>
      </properties>
    </profile>
    <profile>
      <id>enable-adaptive-scheduler</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <systemProperties>
                <flink.tests.enable-adaptive-scheduler>true</flink.tests.enable-adaptive-scheduler>
              </systemProperties>
              <excludedGroups>org.apache.flink.testutils.junit.FailsWithAdaptiveScheduler</excludedGroups>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>java11</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>build-helper-maven-plugin</artifactId>
              <version>1.7</version>
            </plugin>
            <plugin>
              <artifactId>maven-surefire-plugin</artifactId>
              <configuration>
                <excludedGroups>org.apache.flink.testutils.junit.FailsOnJava11</excludedGroups>
              </configuration>
            </plugin>
            <plugin>
              <artifactId>maven-javadoc-plugin</artifactId>
              <configuration>
                <additionalJOptions>
                  <additionalJOption>--add-exports=java.base/sun.net.util=ALL-UNNAMED</additionalJOption>
                  <additionalJOption>--add-exports=java.rmi/sun.rmi.registry=ALL-UNNAMED</additionalJOption>
                </additionalJOptions>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>java11-target</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <source>11</source>
              <target>11</target>
              <compilerArgs>
                <arg>--add-exports=java.base/sun.net.util=ALL-UNNAMED</arg>
                <arg>--add-exports=java.management/sun.management=ALL-UNNAMED</arg>
                <arg>--add-exports=java.rmi/sun.rmi.registry=ALL-UNNAMED</arg>
                <arg>--add-exports=java.security.jgss/sun.security.krb5=ALL-UNNAMED</arg>
              </compilerArgs>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>java17</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-surefire-plugin</artifactId>
              <configuration>
                <excludedGroups>org.apache.flink.testutils.junit.FailsOnJava17</excludedGroups>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
      <properties>
        <scala.version>2.12.15</scala.version>
      </properties>
    </profile>
    <profile>
      <id>java17-target</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <source>17</source>
              <target>17</target>
              <compilerArgs>
                <arg>--add-exports=java.base/sun.net.util=ALL-UNNAMED</arg>
                <arg>--add-exports=java.management/sun.management=ALL-UNNAMED</arg>
                <arg>--add-exports=java.rmi/sun.rmi.registry=ALL-UNNAMED</arg>
                <arg>--add-exports=java.security.jgss/sun.security.krb5=ALL-UNNAMED</arg>
              </compilerArgs>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>fast</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.rat</groupId>
              <artifactId>apache-rat-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>
            <plugin>
              <artifactId>maven-checkstyle-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>
            <plugin>
              <groupId>com.diffplug.spotless</groupId>
              <artifactId>spotless-maven-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>
            <plugin>
              <artifactId>maven-enforcer-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>
            <plugin>
              <artifactId>maven-javadoc-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>
            <plugin>
              <groupId>io.github.zentol.japicmp</groupId>
              <artifactId>japicmp-maven-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>
            <plugin>
              <groupId>org.cyclonedx</groupId>
              <artifactId>cyclonedx-maven-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>check-convergence</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>dependency-convergence</id>
                <phase>${flink.convergence.phase}</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>spotbugs</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.github.hazendaz.spotbugs</groupId>
            <artifactId>spotbugs-maven-plugin</artifactId>
            <version>3.0.6</version>
            <executions>
              <execution>
                <id>findbugs-run</id>
                <phase>compile</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <xmlOutput>true</xmlOutput>
              <threshold>Low</threshold>
              <effort>default</effort>
              <findbugsXmlOutputDirectory>${project.build.directory}/spotbugs</findbugsXmlOutputDirectory>
              <excludeFilterFile>${rootDir}/tools/maven/spotbugs-exclude.xml</excludeFilterFile>
              <failOnError>true</failOnError>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>xml-maven-plugin</artifactId>
            <version>1.0.1</version>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>transform</goal>
                </goals>
              </execution>
            </executions>
            <dependencies>
              <dependency>
                <groupId>com.github.hazendaz.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>3.0.6</version>
              </dependency>
            </dependencies>
            <configuration>
              <transformationSets>
                <transformationSet>
                  <dir>${project.build.directory}/spotbugs</dir>
                  <outputDir>${project.build.directory}/spotbugs</outputDir>
                  <stylesheet>plain.xsl</stylesheet>
                  <fileMappers>
                    <fileMapper>
                      <targetExtension>.html</targetExtension>
                    </fileMapper>
                  </fileMappers>
                </transformationSet>
              </transformationSets>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>docs-and-source</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.2.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.9.1</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <quiet>true</quiet>
                  <detectOfflineLinks>false</detectOfflineLinks>
                  <additionalJOptions>
                    <additionalJOption>-Xdoclint:none</additionalJOption>
                  </additionalJOptions>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <quiet>true</quiet>
              <detectOfflineLinks>false</detectOfflineLinks>
              <additionalJOptions>
                <additionalJOption>-Xdoclint:none</additionalJOption>
              </additionalJOptions>
            </configuration>
          </plugin>
        </plugins>
      </build>
      <properties>
        <os.detected.bitness>64</os.detected.bitness>
        <os.detected.name>osx</os.detected.name>
        <os.detected.classifier>osx-aarch_64</os.detected.classifier>
        <os.detected.arch>aarch_64</os.detected.arch>
      </properties>
    </profile>
    <profile>
      <id>release</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-release-plugin</artifactId>
              <version>2.1</version>
              <configuration>
                <mavenExecutorId>forked-path</mavenExecutorId>
                <useReleaseProfile>false</useReleaseProfile>
                <arguments>-Psonatype-oss-release</arguments>
                <goals>deploy</goals>
                <waitBeforeTagging>10</waitBeforeTagging>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.4</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--digest-algo=SHA512</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <gpgArguments>
                <arg>--digest-algo=SHA512</arg>
              </gpgArguments>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-maven</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireMavenVersion>
                      <version>3.8.6</version>
                    </requireMavenVersion>
                    <requireJavaVersion>
                      <version>1.8.0</version>
                    </requireJavaVersion>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <properties>
        <os.detected.bitness>64</os.detected.bitness>
        <target.java.version>1.8</target.java.version>
        <os.detected.name>osx</os.detected.name>
        <os.detected.classifier>osx-aarch_64</os.detected.classifier>
        <os.detected.arch>aarch_64</os.detected.arch>
      </properties>
    </profile>
  </profiles>
  <modules>
    <module>flink-annotations</module>
    <module>flink-architecture-tests</module>
    <module>flink-core</module>
    <module>flink-java</module>
    <module>flink-scala</module>
    <module>flink-filesystems</module>
    <module>flink-rpc</module>
    <module>flink-runtime</module>
    <module>flink-runtime-web</module>
    <module>flink-optimizer</module>
    <module>flink-streaming-java</module>
    <module>flink-streaming-scala</module>
    <module>flink-connectors</module>
    <module>flink-formats</module>
    <module>flink-examples</module>
    <module>flink-clients</module>
    <module>flink-container</module>
    <module>flink-queryable-state</module>
    <module>flink-tests</module>
    <module>flink-end-to-end-tests</module>
    <module>flink-test-utils-parent</module>
    <module>flink-state-backends</module>
    <module>flink-dstl</module>
    <module>flink-libraries</module>
    <module>flink-table</module>
    <module>flink-quickstart</module>
    <module>flink-contrib</module>
    <module>flink-dist</module>
    <module>flink-dist-scala</module>
    <module>flink-metrics</module>
    <module>flink-yarn</module>
    <module>flink-yarn-tests</module>
    <module>flink-fs-tests</module>
    <module>flink-docs</module>
    <module>flink-python</module>
    <module>flink-walkthroughs</module>
    <module>flink-kubernetes</module>
    <module>flink-external-resources</module>
    <module>tools/ci/flink-ci-tools</module>
  </modules>
  <repositories>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>repository.jboss.org</id>
      <url>https://repository.jboss.org/nexus/content/groups/public/</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.36</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>1.3.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.9.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-params</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-engine</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>5.9.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-platform-engine</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.23.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.4.6</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>3.4.6</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-all</artifactId>
      <version>1.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>1.18.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>testcontainers</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-slf4j-impl</artifactId>
      <version>2.17.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>2.17.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>2.17.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-1.2-api</artifactId>
      <version>2.17.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-shaded-force-shading</artifactId>
        <version>${flink.shaded.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-shaded-asm-9</artifactId>
        <version>9.5-${flink.shaded.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-shaded-guava</artifactId>
        <version>31.1-jre-${flink.shaded.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-shaded-jackson</artifactId>
        <version>${flink.shaded.jackson.version}-${flink.shaded.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-shaded-jackson-module-jsonSchema</artifactId>
        <version>${flink.shaded.jackson.version}-${flink.shaded.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-shaded-netty</artifactId>
        <version>4.1.91.Final-${flink.shaded.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-shaded-netty-tcnative-dynamic</artifactId>
        <version>2.0.59.Final-${flink.shaded.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-common</artifactId>
        <version>${flink.hadoop.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>jdk.tools</artifactId>
            <groupId>jdk.tools</groupId>
          </exclusion>
          <exclusion>
            <artifactId>log4j</artifactId>
            <groupId>log4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-hdfs</artifactId>
        <version>${flink.hadoop.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>jdk.tools</artifactId>
            <groupId>jdk.tools</groupId>
          </exclusion>
          <exclusion>
            <artifactId>log4j</artifactId>
            <groupId>log4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>reload4j</artifactId>
            <groupId>ch.qos.reload4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-reload4j</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-mapreduce-client-core</artifactId>
        <version>${flink.hadoop.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>jdk.tools</artifactId>
            <groupId>jdk.tools</groupId>
          </exclusion>
          <exclusion>
            <artifactId>log4j</artifactId>
            <groupId>log4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>reload4j</artifactId>
            <groupId>ch.qos.reload4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-reload4j</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-common</artifactId>
        <version>${flink.hadoop.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>jdk.tools</artifactId>
            <groupId>jdk.tools</groupId>
          </exclusion>
          <exclusion>
            <artifactId>log4j</artifactId>
            <groupId>log4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-client</artifactId>
        <version>${flink.hadoop.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>jdk.tools</artifactId>
            <groupId>jdk.tools</groupId>
          </exclusion>
          <exclusion>
            <artifactId>log4j</artifactId>
            <groupId>log4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-shaded-zookeeper-3</artifactId>
        <version>${zookeeper.version}-${flink.shaded.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>1.3.9</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-1.2-api</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.12.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-text</artifactId>
        <version>1.10.0</version>
      </dependency>
      <dependency>
        <groupId>org.xerial.snappy</groupId>
        <artifactId>snappy-java</artifactId>
        <version>1.1.10.4</version>
        <exclusions>
          <exclusion>
            <artifactId>org.osgi.core</artifactId>
            <groupId>org.osgi</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.lz4</groupId>
        <artifactId>lz4-java</artifactId>
        <version>${lz4.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.oshi</groupId>
        <artifactId>oshi-core</artifactId>
        <version>6.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.avro</groupId>
        <artifactId>avro</artifactId>
        <version>${avro.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>1.14.4</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-agent</artifactId>
        <version>1.14.4</version>
      </dependency>
      <dependency>
        <groupId>net.java.dev.jna</groupId>
        <artifactId>jna</artifactId>
        <version>5.12.1</version>
      </dependency>
      <dependency>
        <groupId>org.objenesis</groupId>
        <artifactId>objenesis</artifactId>
        <version>2.1</version>
      </dependency>
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>1.1.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson</groupId>
        <artifactId>jackson-bom</artifactId>
        <version>${jackson-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>logging-interceptor</artifactId>
        <version>${okhttp.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.activation</groupId>
        <artifactId>jakarta.activation-api</artifactId>
        <version>1.2.1</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>2.3.2</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit5.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit4.version}</version>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>${assertj.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>commons-cli</groupId>
        <artifactId>commons-cli</artifactId>
        <version>1.5.0</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>commons-collections</groupId>
        <artifactId>commons-collections</artifactId>
        <version>3.2.2</version>
      </dependency>
      <dependency>
        <groupId>commons-configuration</groupId>
        <artifactId>commons-configuration</artifactId>
        <version>1.7</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.15</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-math3</artifactId>
        <version>3.6.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>1.21</version>
        <exclusions>
          <exclusion>
            <artifactId>org.osgi.core</artifactId>
            <groupId>org.osgi</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.javassist</groupId>
        <artifactId>javassist</artifactId>
        <version>3.24.0-GA</version>
      </dependency>
      <dependency>
        <groupId>joda-time</groupId>
        <artifactId>joda-time</artifactId>
        <version>2.5</version>
      </dependency>
      <dependency>
        <groupId>org.joda</groupId>
        <artifactId>joda-convert</artifactId>
        <version>1.7</version>
      </dependency>
      <dependency>
        <groupId>com.esotericsoftware.kryo</groupId>
        <artifactId>kryo</artifactId>
        <version>2.24.0</version>
      </dependency>
      <dependency>
        <groupId>org.scala-lang</groupId>
        <artifactId>scala-library</artifactId>
        <version>${scala.version}</version>
      </dependency>
      <dependency>
        <groupId>org.scala-lang</groupId>
        <artifactId>scala-reflect</artifactId>
        <version>${scala.version}</version>
      </dependency>
      <dependency>
        <groupId>org.scala-lang</groupId>
        <artifactId>scala-compiler</artifactId>
        <version>${scala.version}</version>
      </dependency>
      <dependency>
        <groupId>org.scalatest</groupId>
        <artifactId>scalatest_${scala.binary.version}</artifactId>
        <version>3.0.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>${zookeeper.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>log4j</artifactId>
            <groupId>log4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>*</artifactId>
            <groupId>io.netty</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jline</artifactId>
            <groupId>jline</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>javax.xml.bind</groupId>
        <artifactId>jaxb-api</artifactId>
        <version>${jaxb.api.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.activation</groupId>
        <artifactId>javax.activation-api</artifactId>
        <version>${javax.activation.api.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>4.4.14</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.13</version>
      </dependency>
      <dependency>
        <groupId>org.reflections</groupId>
        <artifactId>reflections</artifactId>
        <version>0.9.10</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-test-utils-junit</artifactId>
        <version>${project.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-architecture-tests-base</artifactId>
        <version>${project.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-architecture-tests-test</artifactId>
        <version>${project.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.lmax</groupId>
        <artifactId>disruptor</artifactId>
        <version>3.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>1.33</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-bom</artifactId>
        <version>4.1.70.Final</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers-bom</artifactId>
        <version>${testcontainers.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.tngtech.archunit</groupId>
        <artifactId>archunit</artifactId>
        <version>${archunit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.tngtech.archunit</groupId>
        <artifactId>archunit-junit5</artifactId>
        <version>${archunit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-module-junit4</artifactId>
        <version>${powermock.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-api-mockito2</artifactId>
        <version>${powermock.version}</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <artifactId>mockito-core</artifactId>
            <groupId>org.mockito</groupId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <flink.XmxUnitTest>768m</flink.XmxUnitTest>
    <jaxb.api.version>2.3.1</jaxb.api.version>
    <spotless.scalafmt.version>3.4.3</spotless.scalafmt.version>
    <maven.compiler.target>${target.java.version}</maven.compiler.target>
    <japicmp.skip>false</japicmp.skip>
    <minikdc.version>3.2.4</minikdc.version>
    <flink.hadoop.version>2.10.2</flink.hadoop.version>
    <lz4.version>1.8.0</lz4.version>
    <powermock.version>2.0.9</powermock.version>
    <junit4.version>4.13.2</junit4.version>
    <chill.version>0.7.6</chill.version>
    <spotless.delimiter>package</spotless.delimiter>
    <japicmp.outputDir>tools/japicmp-output</japicmp.outputDir>
    <avro.version>1.11.1</avro.version>
    <flink.convergence.phase>validate</flink.convergence.phase>
    <flink.XmxMax>3072m</flink.XmxMax>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <japicmp.referenceVersion>1.17.0</japicmp.referenceVersion>
    <mockito.version>3.4.6</mockito.version>
    <flink.shaded.version>17.0</flink.shaded.version>
    <scala.macros.version>2.1.1</scala.macros.version>
    <hamcrest.version>1.3</hamcrest.version>
    <maven.compiler.source>${target.java.version}</maven.compiler.source>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <flink.shaded.jackson.version>2.14.2</flink.shaded.jackson.version>
    <scala.version>2.12.7</scala.version>
    <junit5.version>5.9.1</junit5.version>
    <flink.forkCountUnitTest>4</flink.forkCountUnitTest>
    <testcontainers.version>1.18.3</testcontainers.version>
    <py4j.version>0.10.9.7</py4j.version>
    <javax.activation.api.version>1.2.0</javax.activation.api.version>
    <target.java.version>1.8</target.java.version>
    <flink.surefire.baseArgLine>-XX:+UseG1GC -Xms256m -XX:+IgnoreUnrecognizedVMOptions ${surefire.module.config}</flink.surefire.baseArgLine>
    <scala.binary.version>2.12</scala.binary.version>
    <test.unit.pattern>**/*Test.*</test.unit.pattern>
    <flink.markBundledAsOptional>true</flink.markBundledAsOptional>
    <assertj.version>3.23.1</assertj.version>
    <protoc.version>3.21.7</protoc.version>
    <slf4j.version>1.7.36</slf4j.version>
    <flink.forkCountITCase>2</flink.forkCountITCase>
    <orc.version>1.5.6</orc.version>
    <archunit.version>1.0.0</archunit.version>
    <curator.version>5.4.0</curator.version>
    <flink.reuseForks>true</flink.reuseForks>
    <hive.version>2.3.9</hive.version>
    <spotless.version>2.27.1</spotless.version>
    <log4j.version>2.17.1</log4j.version>
    <zookeeper.version>3.7.1</zookeeper.version>
    <jackson-bom.version>2.14.3</jackson-bom.version>
    <okhttp.version>3.14.9</okhttp.version>
    <spotless.license.header>/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */</spotless.license.header>
    <beam.version>2.43.0</beam.version>
    <flink.XmxITCase>1536m</flink.XmxITCase>
  </properties>
</project>
