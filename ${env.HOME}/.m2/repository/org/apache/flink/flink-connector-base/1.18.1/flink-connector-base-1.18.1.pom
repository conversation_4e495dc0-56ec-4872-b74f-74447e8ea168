<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>flink-connectors</artifactId>
    <groupId>org.apache.flink</groupId>
    <version>1.18.1</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>flink-connector-base</artifactId>
  <name>Flink : Connectors : Base</name>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-core</artifactId>
      <version>1.18.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-table-common</artifactId>
      <version>1.18.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-core</artifactId>
      <version>1.18.1</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-streaming-java</artifactId>
      <version>1.18.1</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-test-utils</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>flink-runtime</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-rpc-akka-loader</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-clients</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-streaming-java</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-statebackend-rocksdb</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-statebackend-changelog</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-dstl-dfs</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-connector-test-utils</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>testcontainers</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-streaming-java</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-architecture-tests-test</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>flink-architecture-tests-base</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>archunit</artifactId>
          <groupId>com.tngtech.archunit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>archunit-junit5</artifactId>
          <groupId>com.tngtech.archunit</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.36</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>1.3.9</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-test-utils-junit</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>testcontainers</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.9.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-params</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-engine</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>5.9.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-platform-engine</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.23.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.4.6</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>3.4.6</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-all</artifactId>
      <version>1.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>1.18.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>testcontainers</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-slf4j-impl</artifactId>
      <version>2.17.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>2.17.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>2.17.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-1.2-api</artifactId>
      <version>2.17.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <properties>
    <surefire.module.config>--add-opens=java.base/java.util=ALL-UNNAMED</surefire.module.config>
  </properties>
</project>
