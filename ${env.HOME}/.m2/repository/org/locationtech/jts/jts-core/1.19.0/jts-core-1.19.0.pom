<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.locationtech.jts</groupId>
        <artifactId>jts-modules</artifactId>
        <version>1.19.0</version>
    </parent>
    <artifactId>jts-core</artifactId>
    <name>${project.groupId}:${project.artifactId}</name>
    <packaging>bundle</packaging>

    <properties>
        <lint>unchecked</lint>
        <doclint>none</doclint>
    </properties>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                  <archive>
                    <manifestEntries>
                      <Automatic-Module-Name>org.locationtech.jts</Automatic-Module-Name>
                    </manifestEntries>
                    <manifestSections>
                      <manifestSection>
                        <name>org.locationtech.jts</name>
                        <manifestEntries>
                          <Sealed>true</Sealed>
                        </manifestEntries>
                      </manifestSection>
                    </manifestSections>
                  </archive>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <unpackBundle>true</unpackBundle>
                    <instructions>
                        <Bundle-SymbolicName>${project.groupId}.${project.artifactId}</Bundle-SymbolicName>
                        <Bundle-Name>${project.artifactId}</Bundle-Name>
                        <Bundle-Version>${project.version}</Bundle-Version>
                        <Export-Package>org.locationtech.jts.*</Export-Package>
                    </instructions>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
