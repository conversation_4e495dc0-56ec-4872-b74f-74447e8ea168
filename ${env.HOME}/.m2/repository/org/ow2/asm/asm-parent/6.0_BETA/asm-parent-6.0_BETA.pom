<!--
 ! ASM: a very small and fast Java bytecode manipulation framework
 ! Copyright (c) 2000-2011 INRIA, France Telecom
 ! All rights reserved.
 !
 ! Redistribution and use in source and binary forms, with or without
 ! modification, are permitted provided that the following conditions
 ! are met:
 ! 1. Redistributions of source code must retain the above copyright
 !    notice, this list of conditions and the following disclaimer.
 ! 2. Redistributions in binary form must reproduce the above copyright
 !    notice, this list of conditions and the following disclaimer in the
 !    documentation and/or other materials provided with the distribution.
 ! 3. Neither the name of the copyright holders nor the names of its
 !    contributors may be used to endorse or promote products derived from
 !    this software without specific prior written permission.
 !
 ! THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 ! AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 ! IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 ! ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 ! LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 ! CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 ! SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 ! INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 ! CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 ! ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 ! THE POSSIBILITY OF SUCH DAMAGE.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                        http://maven.apache.org/maven-v4_0_0.xsd">

  <parent>
    <artifactId>ow2</artifactId>
    <groupId>org.ow2</groupId>
    <version>1.3</version>
  </parent>
 
  <modelVersion>4.0.0</modelVersion>

  <artifactId>asm-parent</artifactId>
  <groupId>org.ow2.asm</groupId>
  <version>6.0_BETA</version>
  <packaging>pom</packaging>

  <name>ASM</name>
  <description>A very small and fast Java bytecode manipulation framework</description>
  <url>http://asm.objectweb.org/</url>
  
  <organization>
    <name>ObjectWeb</name>
    <url>http://www.objectweb.org/</url>
  </organization>
  <inceptionYear>2000</inceptionYear>
  
  <licenses>
    <license>
      <name>BSD</name>
      <url>http://asm.objectweb.org/license.html</url>
    </license>
  </licenses>

  <developers>
    <developer>
      <name>Eric Bruneton</name>
      <id>ebruneton</id>
      <email><EMAIL></email>
      <roles>
        <role>Creator</role>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Eugene Kuleshov</name>
      <id>eu</id>
      <email><EMAIL></email>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Remi Forax</name>
      <id>forax</id>
      <email><EMAIL></email>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
  </developers>

  <scm>
    <connection>scm:svn:svn://svn.forge.objectweb.org/svnroot/asm/trunk</connection>
    <developerConnection>scm:svn:svn+ssh://${maven.username}@svn.forge.objectweb.org/svnroot/asm/trunk</developerConnection>
    <url>http://svn.forge.objectweb.org/cgi-bin/viewcvs.cgi/asm/trunk/</url>
  </scm>
  
  <issueManagement>
    <url>http://forge.objectweb.org/tracker/?group_id=23</url>
  </issueManagement>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <artifactId>asm</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-tree</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-analysis</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-commons</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-util</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-xml</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <mailingLists>
    <mailingList>
      <name>ASM Users List</name>
      <subscribe><EMAIL>?subject=subscribe%20asm</subscribe>
      <unsubscribe><EMAIL>?subject=unsubscribe%20asm</unsubscribe>
      <post><EMAIL></post>
      <archive>http://www.objectweb.org/wws/arc/asm</archive>
    </mailingList>
    <mailingList>
      <name>ASM Team List</name>
      <subscribe><EMAIL>?subject=subscribe%20asm-team</subscribe>
      <unsubscribe><EMAIL>?subject=unsubscribe%20asm-team</unsubscribe>
      <post><EMAIL></post>
      <archive>http://www.objectweb.org/wws/arc/asm-team</archive>
    </mailingList>
  </mailingLists>

</project>
