<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.testcontainers</groupId>
  <artifactId>testcontainers-bom</artifactId>
  <version>1.17.2</version>
  <packaging>pom</packaging>
  <name>Testcontainers :: BOM</name>
  <description>Isolated container management for Java code testing</description>
  <url>https://testcontainers.org</url>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/testcontainers/testcontainers-java/issues</url>
  </issueManagement>
  <licenses>
    <license>
      <name>MIT</name>
      <url>http://opensource.org/licenses/MIT</url>
    </license>
  </licenses>
  <scm>
    <url>https://github.com/testcontainers/testcontainers-java/</url>
    <connection>scm:git:git://github.com/testcontainers/testcontainers-java.git</connection>
    <developerConnection>scm:git:ssh://**************/testcontainers/testcontainers-java.git</developerConnection>
  </scm>
  <developers>
    <developer>
      <id>rnorth</id>
      <name>Richard North</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <dependencies/>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>azure</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>cassandra</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>clickhouse</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>cockroachdb</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>couchbase</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>database-commons</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>db2</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>dynalite</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>elasticsearch</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>gcloud</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>hivemq</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>influxdb</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>jdbc</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>k3s</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>kafka</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>localstack</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mariadb</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mockserver</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mongodb</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mssqlserver</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mysql</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>neo4j</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>nginx</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>oracle-xe</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>orientdb</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>postgresql</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>presto</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>pulsar</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>r2dbc</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>rabbitmq</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>selenium</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>solr</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>spock</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>toxiproxy</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>trino</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>vault</artifactId>
        <version>1.17.2</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
