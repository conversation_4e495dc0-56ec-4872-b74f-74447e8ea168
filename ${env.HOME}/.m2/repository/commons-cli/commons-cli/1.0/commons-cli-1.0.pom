<project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-cli</groupId>
  <artifactId>commons-cli</artifactId>
  <name>CLI</name>
  <version>1.0</version>
  <description>Commons CLI provides a simple API for working with the command line arguments and options.</description>
  <inceptionYear>2002</inceptionYear>
  <developers>
    <developer>
      <id>jstrachan</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>SpiritSoft, Inc.</organization>
    </developer>
    <developer>
      <id>bob</id>
      <name>bob mcwhirter</name>
      <email><EMAIL></email>
      <organization>Werken</organization>
    </developer>
    <developer>
      <id>jkeyes</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>integral Source</organization>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <name><PERSON><PERSON></name>
      <email><EMAIL></email>
      <roles>
        <role>helped in the Avalon CLI merge</role>
      </roles>
    </contributor>
    <contributor>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>He<PERSON><PERSON>-<PERSON></organization>
      <roles>
        <role>supplied patch</role>
      </roles>
    </contributor>
  </contributors>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <includes>
            <include>**/*Test*.java</include>
          </includes>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.7</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>