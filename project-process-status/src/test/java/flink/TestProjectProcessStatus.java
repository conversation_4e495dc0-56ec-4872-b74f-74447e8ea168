package flink;

import com.bees360.flink.ProjectProcessStatusSyncJob;
import com.bees360.flink.entity.ProjectProcessStatus;
import com.bees360.flink.function.LastNodeProcessWindowFunction;
import com.bees360.flink.serialization.KafkaProjectSerializationSchema;
import com.bees360.flink.sink.KafkaSinkBuilderUtil;
import com.bees360.flink.util.EnvUtils;
import com.bees360.flink.util.FileUtils;
import com.bees360.flink.util.Plugins;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.test.util.MiniClusterWithClientResource;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.junit.ClassRule;
import org.junit.Test;

public class TestProjectProcessStatus {

    @ClassRule
    public static MiniClusterWithClientResource flinkCluster =
            new MiniClusterWithClientResource(
                    new MiniClusterResourceConfiguration.Builder()
                            .setNumberSlotsPerTaskManager(2)
                            .setNumberTaskManagers(1)
                            .build());

    public static final String DDL_SQL = "project_process_status_ddl.sql";

    public static final String QUERY_SQL = "project_process_status_sql_query.sql";

    @Test
    public void testSyncProjectProcessStatus() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // configure your test environment
        env.setParallelism(1);
        StreamTableEnvironment streamTableEnv = StreamTableEnvironment.create(env);

        var properties = EnvUtils.getProperties(new String[]{});
        properties.resetKafkaConfig(ProjectProcessStatusSyncJob.JOB_NAME);
        FileUtils.getSql(
                        ProjectProcessStatusSyncJob.class,
                        DDL_SQL,
                        properties,
                        Plugins.loadTopicReplaceHandler(new String[]{}))
                .forEach(streamTableEnv::executeSql);
        Table resultTable =
                streamTableEnv.sqlQuery(
                        FileUtils.getSqlFile(ProjectProcessStatusSyncJob.class, QUERY_SQL));

        var kafkaSink =
                KafkaSinkBuilderUtil.getSinkBuilder(
                                properties,
                                new KafkaProjectSerializationSchema<ProjectProcessStatus>(
                                        properties.getTARGET_KAFKA_TOPIC()))
                        .build();

        // interpret the updating Table as a changelog DataStream
        DataStream<Row> input = streamTableEnv.toChangelogStream(resultTable);
        input.filter(e -> RowKind.INSERT.equals(e.getKind()))
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps())
                .windowAll(TumblingProcessingTimeWindows.of(Time.milliseconds(1000)))
                .process(new LastNodeProcessWindowFunction())
                .map(ProjectProcessStatus::getProject)
                .keyBy(ProjectProcessStatus::getProjectId)
                .print();
        //                .sinkTo(kafkaSink)
        //                .name("sync_to_kafka");
        //         env.execute();
    }
}
