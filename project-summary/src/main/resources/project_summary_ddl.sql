CREATE TABLE project_summary
(
    id          bigint,
    project_id  bigint,
    summary     STRING,
    created_at  TIMESTAMP_LTZ,
    updated_at  TIMESTAMP_LTZ,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
      'connector' = 'kafka',
      'properties.bootstrap.servers' = '$kafka-url',
      'properties.group.id' = '$kafka-group',
      'topic' = '$kafka-topic-prefix.public.project_summary',
      'scan.startup.mode' = 'earliest-offset',
      'value.debezium-json.schema-include' = 'true',
      'value.debezium-json.timestamp-format.standard' = 'ISO-8601',
      'value.format' = 'debezium-json'
      );