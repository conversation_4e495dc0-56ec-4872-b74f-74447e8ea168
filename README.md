# Flink Job Repository

Flink is a repository designed for deploying Flink jobs. It leverages the power of Apache Flink for stream processing and integrates with Flink CDC to handle real-time data pipelines. This project focuses on writing Flink jobs, packaging them into JAR files, and deploying them to a Flink cluster. It utilizes Flink's SQL capabilities to assemble final data and synchronize it with Elasticsearch.

[![pipeline status](https://gitlab.bees360.com/engineers/flink/badges/main/pipeline.svg)](https://gitlab.bees360.com/engineers/flink/commits/main)
[![coverage report](https://gitlab.bees360.com/engineers/flink/badges/main/coverage.svg)](https://gitlab.bees360.com/engineers/flink/commits/main)


## Key Features

- **Apache Flink Integration**: Deploy Flink jobs for efficient stream processing.
- **Flink CDC**: Use Flink's Change Data Capture (CDC) to handle real-time data changes.
- **SQL-based Data Assembly**: Assemble data using Flink SQL for easier management and understanding.
- **Elasticsearch Integration**: Synchronize processed data with Elasticsearch for advanced search and analytics.

## Getting Started

### Downloading Flink

You need to have Java 11 installed. To check the Java version installed, type in your terminal:
```bash
java -version
```

Next, download the latest binary release of Flink, then extract the archive:
```bash
tar -xzf flink-*.tgz
```

### Starting and stopping a local cluster
To start a local cluster, run the bash script that comes with Flink:
```bash
./bin/start-cluster.sh
```
Flink is now running as a background process. You can check its status with the following command:
```bash
ps aux | grep flink
```
You should be able to navigate to the web UI at localhost:8081 to view the Flink dashboard and see that the cluster is up and running.

To quickly stop the cluster and all running components, you can use the provided script:
```bash
./bin/stop-cluster.sh
```

### Building Flink job jar file
```bash
mvn clean install -DskipTests
```
### Submitting Flink job
Flink provides a CLI tool, bin/flink, that can run programs packaged as Java ARchives (JAR) and control their execution. Submitting a job means uploading the job’s JAR ﬁle and related dependencies to the running Flink cluster and executing it.

To deploy the `eberl360-flink-image` job to the running cluster, issue the following command:
```bash
./bin/flink run image/target/eberl360-flink-image-1.0.0-LOCAL.jar
```
You can verify the output by viewing the logs:
```bash
tail log/flink-*-taskexecutor-*.out
```
Additionally, you can check Flink’s web UI to monitor the status of the cluster and running job.

### 版本号规范

当前项目采用4位版本号加一位测试版本规范例如

正常版本号：release-*******

测试版本号：test-*******.5

1所在位置表示仓库版本

2所在位置表示架构/基础设施变动版本

3所在位置表示基础配置版本，例如调整了EnvUtils等模块，导致所有任务的配置项都受影响即升级此版本号

4所在位置表示任务功能版本，如果仅仅是单个任务的功能变动，升级此版本号即可

5所在位置测试版本号，测试版本号仅用于测试阶段，发布时不允许包含此版本号（特殊情况需要先沟通）
