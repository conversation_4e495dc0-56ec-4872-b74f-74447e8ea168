package com.bees360.flink;

import com.alibaba.fastjson.JSON;
import com.bees360.flink.entity.Project;
import com.bees360.flink.entity.ProjectId;
import com.bees360.flink.serialization.SerializationEntry;
import com.bees360.flink.sink.OpenSearchSinkUtil;
import com.bees360.flink.util.EnvUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ProjectKafkaToEsJob {

    private static final Logger LOG = LoggerFactory.getLogger(ProjectKafkaToEsJob.class);

    public static final String JOB_NAME = "kafka_to_es_sync_job";

    public static final String ES_FLUSH_MAX_ACTIONS_ARG = "es.flush_max_actions";
    public static final String EXTRA_KAFKA_TOPICS = "extra_kafka_topics";

    public static void main(String[] args) throws Exception {
        // create environments of both APIs
        var env = EnvUtils.initJobExecutionEnv();

        List<String> kafkaTopics = new ArrayList<>(EnvUtils.jobTopicMap.values());
        ParameterTool parameterTool = EnvUtils.getParameterToolFromArgs(args);
        String maxActions = parameterTool.get(ES_FLUSH_MAX_ACTIONS_ARG, "1000");
        int maxActionsNum = Integer.parseInt(maxActions);
        String extraTopics = parameterTool.get(EXTRA_KAFKA_TOPICS, "");
        Arrays.stream(extraTopics.split(","))
                .filter(StringUtils::isNotBlank)
                .forEach(kafkaTopics::add);
        LOG.info("max action number to es: {}", maxActionsNum);
        LOG.info("topics to sync: {}", kafkaTopics);

        env.enableCheckpointing(180000);

        var properties = EnvUtils.getProperties(args);
        properties.resetKafkaConfig(JOB_NAME);

        KafkaSource<String> source =
                KafkaSource.<String>builder()
                        .setBootstrapServers(properties.getSrcKafkaUrl())
                        .setTopics(kafkaTopics)
                        .setGroupId(properties.SRC_KAFKA_GROUP)
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setValueOnlyDeserializer(new SimpleStringSchema())
                        .setProperties(properties.getSourceKafkaProperties())
                        .setProperty("partition.discovery.interval.ms", "10000")
                        .setProperty("commit.offsets.on.checkpoint", "true")
                        .setProperty("auto.offset.reset", "latest")
                        .build();

        var sinkBuilder =
                OpenSearchSinkUtil.getSinkBuilder(properties, Project::createUpdateIndexRequest);
        sinkBuilder.setBulkFlushMaxActions(maxActionsNum);
        sinkBuilder.setBulkFlushInterval(500);
        sinkBuilder.setBulkFlushMaxSizeMb(100);

        var input =
                env.fromSource(source, WatermarkStrategy.forMonotonousTimestamps(), "kafka_source");
        input.setParallelism(1)
                .map(
                        e -> {
                            var projectId = JSON.parseObject(e, ProjectId.class);
                            return new SerializationEntry(projectId.getProjectId(), e);
                        })
                .setParallelism(1)
                .keyBy(SerializationEntry::getKey)
                .addSink(sinkBuilder.build())
                .name("sync_to_es");
        env.execute(JOB_NAME);
    }
}
