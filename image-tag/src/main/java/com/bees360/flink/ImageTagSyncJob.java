package com.bees360.flink;

import com.bees360.flink.entity.ImageTag;
import com.bees360.flink.serialization.KafkaImageSerializationSchema;
import com.bees360.flink.sink.KafkaSinkBuilderUtil;
import com.bees360.flink.util.EnvUtils;
import com.bees360.flink.util.FileUtils;
import org.apache.flink.configuration.PipelineOptions;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;

import java.util.Objects;

public class ImageTagSyncJob {
    public static final String JOB_NAME = "image_tag_sync_job";
    public static final String DDL_SQL = "image_tag_sql_ddl.sql";
    public static final String QUERY_SQL = "image_tag_sql_query.sql";
    public static final String ANNOTATION_TAG_TYPE = "Annotation";

    public static void main(String[] args) throws Exception {
        // Create environments for both APIs
        var env = EnvUtils.initJobExecutionEnv();
        // Use RocksDB as the state backend
        env.setStateBackend(new EmbeddedRocksDBStateBackend());
        StreamTableEnvironment streamTableEnv = StreamTableEnvironment.create(env);
        streamTableEnv.getConfig().set(PipelineOptions.NAME, JOB_NAME);

        // Get image-related properties from command line arguments
        var properties = EnvUtils.getImageProperties(args);
        properties.resetKafkaConfig(JOB_NAME);

        // Load and execute the DDL SQL statements
        FileUtils.getSql(ImageTagSyncJob.class, DDL_SQL, properties)
                .forEach(streamTableEnv::executeSql);

        // Execute the query SQL to get the result table
        Table resultTable =
                streamTableEnv.sqlQuery(FileUtils.getSqlFile(ImageTagSyncJob.class, QUERY_SQL));

        // Create a Kafka sink for the image tags
        var kafkaSink =
                KafkaSinkBuilderUtil.getImageSinkBuilder(
                                properties,
                                new KafkaImageSerializationSchema<ImageTag>(
                                        properties.getTARGET_KAFKA_TOPIC()))
                        .build();

        // Interpret the updating Table as a changelog DataStream
        DataStream<Row> input = streamTableEnv.toChangelogStream(resultTable);

        // Filter out rows with tag_type equal to "Annotation"
        input.filter(
                        e ->
                                !Objects.equals(
                                        e.getField("tag_type"),
                                        ANNOTATION_TAG_TYPE)) // Filter out Annotation data
                .filter(e -> RowKind.INSERT.equals(e.getKind())) // Only keep INSERT rows
                .map(ImageTag::getImageTag) // Map to ImageTag objects
                .filter(x -> x.getImageId() != null) // Filter out ImageTags with null imageId
                .sinkTo(kafkaSink) // Sink to Kafka
                .name("image_sync_to_kafka");

        // Execute the environment
        env.execute();
    }
}
