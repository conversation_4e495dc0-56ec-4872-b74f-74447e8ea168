select CAST(p.group_id as bigint)                                          as project_id,
       CAST(coalesce(p2.roof_image_count, 0) as int)                       as roof_image_count,
       CAST(coalesce(p2.elevation_image_count, 0) as int)                  as elevation_image_count,
       CAST(coalesce(p2.interior_image_count, 0) as int)                   as interior_image_count,
       CAST(coalesce(p.all_image_count, 0) as int) - CAST(coalesce(p2.roof_image_count, 0) as int) -
       CAST(coalesce(p2.elevation_image_count, 0) as int) -
       CAST(coalesce(p2.interior_image_count, 0) as int)                   as other_image_count,
       CAST(coalesce(p.all_image_count, 0) as int)                         as all_image_count,
       coalesce(p2.max_image_tag_updated_at, p.max_image_group_updated_at) as max_updated_at
from (select i.group_id,
             count(distinct case when i.is_deleted = false then i.image_id end) as all_image_count,
             max(i.updated_at)                                                  as max_image_group_updated_at
      from (select ig.group_id, ig.image_id, ig.is_deleted, ig.updated_at
            from image_group ig
            where ig.group_type = 'GROUP_PROJECT'
              and CAST(ig.group_id as bigint) >= CAST('$project-image-start-sync-project-id' as bigint)) i
      group by i.group_id) as p
         left join (select i.group_id,
                           count(distinct case when i.tag_id = 100 and i.is_deleted = 0 then i.image_id end)  as roof_image_count,
                           count(distinct case when i.tag_id = 1102 and i.is_deleted = 0 then i.image_id end) as elevation_image_count,
                           count(distinct case when i.tag_id = 130 and i.is_deleted = 0 then i.image_id end)  as interior_image_count,
                           max(i.updated_at)                                                                  as max_image_tag_updated_at
                    from (select ig.group_id, ig.image_id, itr.tag_id, itr.is_deleted,
                                 coalesce(itr.deleted_at, itr.created_at) as updated_at
                          from image_tag_relation itr
                                   left join (select ig.group_id, ig.image_id
                                              from image_group ig
                                              where ig.group_type = 'GROUP_PROJECT'
                                                and CAST(ig.group_id as bigint) >= CAST('$project-image-start-sync-project-id' as bigint)
                                                and ig.is_deleted = false) ig
                                             on ig.image_id = itr.image_id
                          where ig.image_id is not null) i
                    group by i.group_id) as p2 on p.group_id = p2.group_id;