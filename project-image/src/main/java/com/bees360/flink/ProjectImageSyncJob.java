package com.bees360.flink;

import com.bees360.flink.entity.ProjectImage;
import com.bees360.flink.serialization.KafkaProjectSerializationSchema;
import com.bees360.flink.sink.KafkaSinkBuilderUtil;
import com.bees360.flink.util.EnvUtils;
import com.bees360.flink.util.FileUtils;
import com.bees360.flink.util.TimeUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.PipelineOptions;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

public class ProjectImageSyncJob {
    private static final Logger LOG = LoggerFactory.getLogger(ProjectImageSyncJob.class);
    public static final String JOB_NAME = "project_image_sync_job";
    public static final String DDL_SQL = "project_image_ddl.sql";
    public static final String QUERY_SQL = "project_image_sql_query.sql";
    public static final String START_SYNC_DATA_DATETIME_ARG = "start_sync_data_datetime";
    public static final String TOLERABLE_FAILED_CHECKPOINTS_ARG = "tolerable_failed_checkpoints";
    public static final String START_SYNC_PROJECT_ID_ARG = "start_sync_project_id";
    public static final String PROJECT_IMAGE_START_SYNC_PROJECT_ID_PLACEHOLDER =
            "$project-image-start-sync-project-id";

    public static void main(String[] args) throws Exception {
        // Create environments for both APIs
        var env = EnvUtils.initJobExecutionEnv();
        // Allow checkpoints to start without alignment
        env.getCheckpointConfig().enableUnalignedCheckpoints(true);

        // Get start_sync_data_datetime argument
        ParameterTool parameterTool = EnvUtils.getParameterToolFromArgs(args);
        String startSyncDataDatetimeStr =
                parameterTool.get(START_SYNC_DATA_DATETIME_ARG, "2024-06-01T00:00:00");
        LocalDateTime startSyncDataDatetime = LocalDateTime.parse(startSyncDataDatetimeStr);
        LOG.info("ProjectImageSyncJob startSyncDataDatetime: {}", startSyncDataDatetime);

        // Get tolerable failed checkpoints argument
        String tolerableFailedCheckpointsStr =
                parameterTool.get(TOLERABLE_FAILED_CHECKPOINTS_ARG, "200");
        int tolerableFailedCheckpoints = Integer.parseInt(tolerableFailedCheckpointsStr);
        LOG.info("ProjectImageSyncJob tolerableFailedCheckpoints: {}", tolerableFailedCheckpoints);
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(tolerableFailedCheckpoints);

        // Get start_sync_project_id argument
        String startSyncProjectId = parameterTool.get(START_SYNC_PROJECT_ID_ARG, "294715");
        LOG.info("ProjectImageSyncJob startSyncProjectId: {}", startSyncProjectId);

        StreamTableEnvironment streamTableEnv = StreamTableEnvironment.create(env);
        var tableEnvConfig = streamTableEnv.getConfig();
        tableEnvConfig.set(PipelineOptions.NAME, JOB_NAME);
        tableEnvConfig.set("table.exec.mini-batch.enabled", "true");
        tableEnvConfig.set("table.exec.mini-batch.allow-latency", "1000 ms");
        tableEnvConfig.set("table.exec.mini-batch.size", "1000");

        var properties = EnvUtils.getProperties(args);
        properties.resetKafkaConfig(JOB_NAME);

        // Create CDC table
        FileUtils.getSql(ProjectImageSyncJob.class, DDL_SQL, properties)
                .forEach(streamTableEnv::executeSql);

        // Execute query SQL
        var querySql =
                FileUtils.getSqlFile(ProjectImageSyncJob.class, QUERY_SQL)
                        .replace(
                                PROJECT_IMAGE_START_SYNC_PROJECT_ID_PLACEHOLDER,
                                startSyncProjectId);
        Table resultTable = streamTableEnv.sqlQuery(querySql);

        // Create Kafka sink
        var kafkaSink =
                KafkaSinkBuilderUtil.getSinkBuilder(
                                properties,
                                new KafkaProjectSerializationSchema<ProjectImage>(
                                        properties.getTARGET_KAFKA_TOPIC()))
                        .build();

        // Interpret the updating Table as a changelog DataStream
        DataStream<Row> input = streamTableEnv.toChangelogStream(resultTable);
        input.filter(e -> RowKind.INSERT.equals(e.getKind())) // Filter for INSERT rows
                .filter(
                        e ->
                                needSyncToEs(
                                        e,
                                        startSyncDataDatetime)) // Check if synchronization is
                // needed
                .map(ProjectImage::getProject) // Map to ProjectImage objects
                .keyBy(ProjectImage::getProjectId) // Key by Project ID
                .sinkTo(kafkaSink) // Sink to Kafka
                .name("sync_to_kafka");

        // Execute the environment
        env.execute();
    }

    // Determine if synchronization to Elasticsearch is needed based on the max_updated_at field and
    // other criteria
    private static boolean needSyncToEs(Row e, LocalDateTime startSyncDataDatetime) {
        var max_updated_at = TimeUtils.ToLocalDateTimeGeneric(e.getField("max_updated_at"));
        var otherImageCount = (Integer) e.getField("other_image_count");
        return max_updated_at != null
                && !max_updated_at.isBefore(
                startSyncDataDatetime) // Check if updated after the start date
                && otherImageCount != null
                && otherImageCount >= 0; // Ensure other image count is valid
    }
}
