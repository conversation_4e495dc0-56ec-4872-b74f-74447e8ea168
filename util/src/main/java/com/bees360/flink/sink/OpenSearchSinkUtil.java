package com.bees360.flink.sink;

import com.bees360.flink.util.EnvUtils;
import com.bees360.flink.util.SinkFunction;
import org.apache.flink.opensearch.shaded.org.apache.http.HttpHost;
import org.apache.flink.opensearch.shaded.org.apache.http.auth.AuthScope;
import org.apache.flink.opensearch.shaded.org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.flink.opensearch.shaded.org.apache.http.client.CredentialsProvider;
import org.apache.flink.opensearch.shaded.org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.flink.opensearch.shaded.org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.flink.opensearch.shaded.org.opensearch.action.update.UpdateRequest;
import org.apache.flink.streaming.connectors.opensearch.OpensearchSinkFunction;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.Serializable;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;

public class OpenSearchSinkUtil {

    private OpenSearchSinkUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static <T extends Serializable>
    org.apache.flink.streaming.connectors.opensearch.OpensearchSink.Builder<T>
    getSinkBuilder(
            EnvUtils.Properties properties,
            SinkFunction<T, UpdateRequest> function) {
        List<HttpHost> httpHosts = new ArrayList<>();
        HttpHost http =
                new HttpHost(
                        "elasticsearch",
                        9200,
                        "http");
        httpHosts.add(http);

        // Create a builder for the Opensearch sink
        var builder =
                new org.apache.flink.streaming.connectors.opensearch.OpensearchSink.Builder<>(
                        httpHosts,
                        (OpensearchSinkFunction<T>)
                                (element, context, indexer) ->
                                        indexer.add(function.apply(element)));

        builder.setRestClientFactory(
                restClientBuilder ->
                        restClientBuilder.setHttpClientConfigCallback(
                                httpAsyncClientBuilder -> {
                                    // Set up basic authentication if user and password are provided
                                    if (properties.ELASTICSEARCH_USER != null
                                            && properties.ELASTICSEARCH_PASSWORD != null) {
                                        CredentialsProvider credentialsProvider =
                                                new BasicCredentialsProvider();
                                        credentialsProvider.setCredentials(
                                                AuthScope.ANY,
                                                new UsernamePasswordCredentials(
                                                        properties.ELASTICSEARCH_USER,
                                                        properties.ELASTICSEARCH_PASSWORD));
                                        httpAsyncClientBuilder.setDefaultCredentialsProvider(
                                                credentialsProvider);
                                    }
                                    // If configured to trust all certificates, set the SSL context
                                    if (properties.TRUST_ALL_CERTS) {
                                        return trustAllCerts(httpAsyncClientBuilder);
                                    }
                                    return httpAsyncClientBuilder;
                                }));

        // These parameters control the write performance to Elasticsearch
        builder.setBulkFlushMaxActions(
                100); // Instructs the sink to emit after every element; otherwise, they would be
        // buffered
        builder.setBulkFlushInterval(1000); // Set the interval for flushing
        builder.setBulkFlushMaxSizeMb(100); // Set the maximum size for flushing in MB

        return builder;
    }

    // Method to trust all SSL certificates
    @SuppressWarnings({"java:S4423", "java:S4830", "java:S5527"})
    private static HttpAsyncClientBuilder trustAllCerts(HttpAsyncClientBuilder builder) {
        builder.setSSLHostnameVerifier((s, sslSession) -> true);
        try {
            var sc = SSLContext.getInstance("SSL");
            var trustAllCerts =
                    new TrustManager[]{
                            new X509TrustManager() {
                                public X509Certificate[] getAcceptedIssuers() {
                                    return null;
                                }

                                public void checkClientTrusted(
                                        X509Certificate[] certs, String authType) {
                                }

                                public void checkServerTrusted(
                                        X509Certificate[] certs, String authType) {
                                }
                            }
                    };
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            return builder.setSSLContext(sc);
        } catch (Exception ex) {
            throw new IllegalStateException(ex);
        }
    }
}
