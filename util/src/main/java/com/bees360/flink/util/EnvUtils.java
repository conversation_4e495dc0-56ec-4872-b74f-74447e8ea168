package com.bees360.flink.util;

import lombok.Data;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Nullable;

public class EnvUtils {

    private EnvUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static final Map<String, String> jobTopicMap =
            Map.ofEntries(
                    Map.entry("project_pipeline_sync_job", "project-pipeline-topic"),
                    Map.entry("project_sync_job", "project-topic"),
                    Map.entry("project_external_sync_job", "project-external-topic"),
                    Map.entry("project_policy_sync_job", "project-policy-topic"),
                    Map.entry("project_state_sync_job", "project-state-topic"),
                    Map.entry("project_status_sync_job", "project-status-topic"),
                    Map.entry("project_status_only_sync_job", "project-status-only-topic"),
                    Map.entry("project_user_sync_job", "project-user-topic"),
                    Map.entry("project_process_status_sync_job", "project-process-status-topic"),
                    Map.entry("project_image_sync_job", "project-image-topic"),
                    Map.entry("project_summary_sync_job", "project-summary-topic"),
                    Map.entry("project_bundle_sync_job", "project-bundle-topic"));

    public static final Map<String, String> imageJobTopicMap =
            Map.ofEntries(
                    Map.entry("image_metadata_sync_job", "image-metadata-topic"),
                    Map.entry("image_resource_sync_job", "image-resource-topic"),
                    Map.entry("image_tag_sync_job", "image-tag-topic"),
                    Map.entry("image_annotation_sync_job", "image-annotation-topic"));

    // Get properties from command line arguments
    public static Properties getProperties(String[] args) {
        return Properties.initProperties(args);
    }

    // Get image-related properties from command line arguments
    public static Properties getImageProperties(String[] args) {
        Properties properties = getProperties(args);
        properties.resetImageConfig();
        return properties;
    }

    // Initialize the job execution environment
    public static StreamExecutionEnvironment initJobExecutionEnv() {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.enableCheckpointing(600000); // Enable checkpointing every 10 minutes
        env.setRestartStrategy(
                RestartStrategies.fixedDelayRestart(1000, 60000)); // Restart strategy on failure
        env.setMaxParallelism(10);
        // Allow checkpoints to start without alignment
        env.getCheckpointConfig().setCheckpointTimeout(600000); // Set checkpoint timeout
        env.getCheckpointConfig()
                .setMinPauseBetweenCheckpoints(180000); // Minimum pause between checkpoints
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1); // Maximum concurrent checkpoints
        // Allow a high number of checkpoint failures during initialization due to large data
        // volumes
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(100);
        var executionConfig = env.getConfig();
        executionConfig.setUseSnapshotCompression(true); // Enable snapshot compression
        return env;
    }

    // Get ParameterTool from command line arguments
    public static ParameterTool getParameterToolFromArgs(String[] args) {
        String[] args2 =
                Arrays.stream(args)
                        .flatMap(arg -> Arrays.stream(arg.split(" ")))
                        .toArray(String[]::new);
        return ParameterTool.fromArgs(args2);
    }

    // Get a specific property from command line arguments with a default value
    public static String getPropertyFromArgs(String[] args, String key, String defaultValue) {
        ParameterTool parameterTool = getParameterToolFromArgs(args);
        return parameterTool.get(key, defaultValue);
    }

    // Get a specific property from command line arguments
    public static String getPropertyFromArgs(String[] args, String key) {
        ParameterTool parameterTool = getParameterToolFromArgs(args);
        return parameterTool.get(key);
    }

    // Get multiple properties from command line arguments
    public static Map<String, String> getPropertiesFromArgs(String[] args, String... keys) {
        ParameterTool parameterTool = getParameterToolFromArgs(args);
        Map<String, String> properties = new HashMap<>();
        for (String key : keys) {
            properties.put(key, parameterTool.get(key));
        }
        return properties;
    }

    // Get a property from system properties with a default value
    public static String getPropertyFromSystem(String key, String defaultValue) {
        return ParameterTool.fromSystemProperties().get(key, defaultValue);
    }

    // Get a property from system properties
    public static String getPropertyFromSystem(String key) {
        return ParameterTool.fromSystemProperties().get(key);
    }

    // Get multiple properties from command line arguments
    public static Map<String, String> getProperties(String[] args, List<String> keys) {
        return ParameterHelper.initParameterHelper(args).getProperties(keys);
    }

    // Get multiple properties with default values from command line arguments
    public static Map<String, String> getPropertiesWithDefault(
            String[] args, Map<String, String> keyDefaultValueMap) {
        return ParameterHelper.initParameterHelper(args)
                .getPropertiesWithDefault(keyDefaultValueMap);
    }

    @Data
    public static class Properties implements Serializable {
        private static ParameterHelper parameterHelper;
        public String PG_HOST;
        public String PG_PORT;
        public String PG_USER;
        public String PG_PASSWORD;
        public String PG_DATABASE_NAME;
        public String PG_SCHEMA_NAME;
        public String PG_SLOT_VERSION;
        public String MYSQL_HOST;
        public String MYSQL_PORT;
        public String MYSQL_USER;
        public String MYSQL_PASSWORD;
        public String MYSQL_DATABASE;
        public String ELASTICSEARCH_HOST;
        public String ELASTICSEARCH_SCHEME;
        public String ELASTICSEARCH_PORT;
        public String ELASTICSEARCH_USER;
        public String ELASTICSEARCH_PASSWORD;
        public boolean TRUST_ALL_CERTS;
        public String SRC_KAFKA_USERNAME;
        public String SRC_KAFKA_PASSWORD;
        public String SRC_KAFKA_TOPIC_PREFIX;
        public String SRC_KAFKA_TOPIC;
        public String SRC_KAFKA_GROUP;
        public String SRC_KAFKA_URL;
        public String TARGET_KAFKA_USERNAME;
        public String TARGET_KAFKA_PASSWORD;
        public String TARGET_KAFKA_TOPIC_PREFIX;
        public String TARGET_KAFKA_TOPIC;
        public String TARGET_KAFKA_GROUP;
        public String TARGET_KAFKA_URL;

        // Props for activity migration: start
        public String MONGO_URL;
        public String MONGO_DATABASE;
        public String ACT_PG_URL;
        public String ACT_PG_HOST;
        public String ACT_PG_PORT;
        public String ACT_PG_DATABASE_NAME;
        public String ACT_PG_USER;
        public String ACT_PG_PASSWORD;
        public String ACT_ACTIVITY_IDS;
        public String ACT_COMMENT_IDS;
        // Props for activity migration: end

        private boolean isInitialized = false;

        /** Priority: args > env */
        public static Properties initProperties(String[] args) {
            Properties properties = new Properties();
            parameterHelper = ParameterHelper.initParameterHelper(args);
            properties.PG_HOST = parameterHelper.getValue("PG_HOST", "postgres");
            properties.PG_PORT = parameterHelper.getValue("PG_PORT", "5432");
            properties.PG_USER = parameterHelper.getValue("PG_USER", "db_user");
            properties.PG_PASSWORD = parameterHelper.getValue("PG_PASSWORD", "db_password");
            properties.PG_DATABASE_NAME = parameterHelper.getValue("PG_DATABASE_NAME", "local_db");
            properties.PG_SCHEMA_NAME = parameterHelper.getValue("PG_SCHEMA_NAME", "public");
            properties.PG_SLOT_VERSION = parameterHelper.getValue("PG_SLOT_VERSION", "");
            properties.ELASTICSEARCH_HOST =
                    parameterHelper.getValue("ELASTICSEARCH_HOST", "elasticsearch");
            properties.ELASTICSEARCH_SCHEME = parameterHelper.getValue("ELASTICSEARCH_SCHEME", "HTTP");
            properties.ELASTICSEARCH_PORT = parameterHelper.getValue("ELASTICSEARCH_PORT", "9200");
            properties.ELASTICSEARCH_USER = parameterHelper.getValue("ELASTICSEARCH_USER", null);
            properties.ELASTICSEARCH_PASSWORD = parameterHelper.getValue("ELASTICSEARCH_PASSWORD", null);
            properties.TRUST_ALL_CERTS =
                    Boolean.parseBoolean(parameterHelper.getValue("TRUST_ALL_CERTS", "false"));
            properties.SRC_KAFKA_USERNAME = parameterHelper.getValue("SRC_KAFKA_USERNAME", "");
            properties.SRC_KAFKA_PASSWORD = parameterHelper.getValue("SRC_KAFKA_PASSWORD", "");
            properties.SRC_KAFKA_TOPIC_PREFIX =
                    parameterHelper.getValue("SRC_KAFKA_TOPIC_PREFIX", "db-sync");
            properties.SRC_KAFKA_TOPIC = parameterHelper.getValue("SRC_KAFKA_TOPIC", "");
            properties.SRC_KAFKA_GROUP = parameterHelper.getValue("SRC_KAFKA_GROUP", "");
            properties.SRC_KAFKA_URL = parameterHelper.getValue("SRC_KAFKA_URL", "kafka-http:9092");
            properties.TARGET_KAFKA_USERNAME = parameterHelper.getValue("TARGET_KAFKA_USERNAME", "");
            properties.TARGET_KAFKA_PASSWORD = parameterHelper.getValue("TARGET_KAFKA_PASSWORD", "");
            properties.TARGET_KAFKA_TOPIC_PREFIX =
                    parameterHelper.getValue("TARGET_KAFKA_TOPIC_PREFIX", "db-sync");
            properties.TARGET_KAFKA_TOPIC = parameterHelper.getValue("TARGET_KAFKA_TOPIC", "");
            properties.TARGET_KAFKA_GROUP = parameterHelper.getValue("TARGET_KAFKA_GROUP", "");
            properties.TARGET_KAFKA_URL = parameterHelper.getValue("TARGET_KAFKA_URL", "kafka-http:9092");
            properties.MYSQL_HOST = parameterHelper.getValue("MYSQL_HOST", "mysql");
            properties.MYSQL_PORT = parameterHelper.getValue("MYSQL_PORT_FLINK", "3306");
            properties.MYSQL_USER = parameterHelper.getValue("MYSQL_USER", "root");
            properties.MYSQL_PASSWORD = parameterHelper.getValue("MYSQL_PASSWORD", "123456");
            properties.MYSQL_DATABASE = parameterHelper.getValue("MYSQL_DATABASE", "Bees360");

            // Props init for activity migration: start
            properties.MONGO_URL = parameterHelper.getValue("MONGO_URL", "mongodb://mongo:27017");
            properties.MONGO_DATABASE = parameterHelper.getValue("MONGO_DATABASE", "activity");
            properties.ACT_PG_URL = parameterHelper.getValue("ACT_PG_URL", "*******************************************");
            properties.ACT_PG_HOST = parameterHelper.getValue("ACT_PG_HOST", "postgres");
            properties.ACT_PG_PORT = parameterHelper.getValue("ACT_PG_PORT", "5432");
            properties.ACT_PG_USER = parameterHelper.getValue("ACT_PG_USER", "activity_db_user");
            properties.ACT_PG_PASSWORD = parameterHelper.getValue("ACT_PG_PASSWORD", "activity_db_user_pass");
            properties.ACT_ACTIVITY_IDS = parameterHelper.getValue("ACT_ACTIVITY_IDS", "");
            properties.ACT_COMMENT_IDS = parameterHelper.getValue("ACT_COMMENT_IDS", "");
            // Props init for activity migration: start

            properties.isInitialized = true;
            return properties;
        }


        public boolean isInitialized() {
            return isInitialized;
        }

        public java.util.Properties getTargetKafkaProperties() {
            var properties = new java.util.Properties();
            properties.setProperty("username", TARGET_KAFKA_USERNAME);
            properties.setProperty("password", TARGET_KAFKA_PASSWORD);
            properties.setProperty("value.converter.schemas.enable", "true");
            return properties;
        }

        public java.util.Properties getSourceKafkaProperties() {
            var properties = new java.util.Properties();
            properties.setProperty("username", SRC_KAFKA_USERNAME);
            properties.setProperty("password", SRC_KAFKA_PASSWORD);
            properties.setProperty("value.converter.schemas.enable", "true");
            return properties;
        }

        public String getSrcKafkaUrl() {
            if (StringUtils.isNoneBlank(SRC_KAFKA_URL)) {
                return SRC_KAFKA_URL;
            }
            return "";
        }

        public String getTargetKafkaUrl() {
            if (StringUtils.isNoneBlank(TARGET_KAFKA_URL)) {
                return TARGET_KAFKA_URL;
            }
            return "";
        }

        /**
         * Customize Kafka configuration based on job name If a specific topic is configured in the
         * environment variables, use that; otherwise, read from {@link EnvUtils#jobTopicMap}
         */
        public void resetKafkaConfig(String jobName) {
            if (StringUtils.isBlank(TARGET_KAFKA_TOPIC)) {
                if (EnvUtils.jobTopicMap.containsKey(jobName)) {
                    this.TARGET_KAFKA_TOPIC = EnvUtils.jobTopicMap.get(jobName);
                } else {
                    this.TARGET_KAFKA_TOPIC = EnvUtils.imageJobTopicMap.get(jobName);
                }
            }
            String groupVersion = parameterHelper.getValue("GROUP_VERSION");
            String kafkaGroup;
            if (StringUtils.isNoneBlank(groupVersion)) {
                kafkaGroup = jobName + "_" + groupVersion;
            } else {
                kafkaGroup = jobName;
            }
            if (StringUtils.isBlank(SRC_KAFKA_GROUP)) {
                this.SRC_KAFKA_GROUP = kafkaGroup;
            }
            if (StringUtils.isBlank(TARGET_KAFKA_GROUP)) {
                this.TARGET_KAFKA_GROUP = kafkaGroup;
            }
        }

        public void resetImageConfig() {
            // If image_es_url related configurations exist, prioritize those; otherwise, use the
            // old es_url
            var imageElasticsearchHost = parameterHelper.getValue("IMAGE_ELASTICSEARCH_HOST");
            var imageElasticsearchScheme = parameterHelper.getValue("IMAGE_ELASTICSEARCH_SCHEME");
            var imageElasticsearchPort = parameterHelper.getValue("IMAGE_ELASTICSEARCH_PORT");
            var imageElasticsearchUser = parameterHelper.getValue("IMAGE_ELASTICSEARCH_USER");
            var imageElasticsearchPassword =
                    parameterHelper.getValue("IMAGE_ELASTICSEARCH_PASSWORD");
            if (imageElasticsearchHost != null
                    && imageElasticsearchScheme != null
                    && imageElasticsearchPort != null) {
                this.ELASTICSEARCH_HOST = imageElasticsearchHost;
                this.ELASTICSEARCH_SCHEME = imageElasticsearchScheme;
                this.ELASTICSEARCH_PORT = imageElasticsearchPort;
                this.ELASTICSEARCH_USER = imageElasticsearchUser;
                this.ELASTICSEARCH_PASSWORD = imageElasticsearchPassword;
            }
        }
    }

    public static class ParameterHelper {
        private final ParameterTool envParameterTool;
        private final ParameterTool argsParameterTool;

        private ParameterHelper(String[] args) {
            this.envParameterTool = ParameterTool.fromMap(System.getenv());
            this.argsParameterTool = getParameterToolFromArgs(args);
        }

        public static ParameterHelper initParameterHelper(String[] args) {
            return new ParameterHelper(args);
        }

        @Nullable
        public String getValue(String key) {
            return argsParameterTool.get(key, envParameterTool.get(key));
        }

        public String getValue(String key, String defaultValue) {
            return argsParameterTool.get(key, envParameterTool.get(key, defaultValue));
        }

        public Map<String, String> getProperties(List<String> keys) {
            Map<String, String> properties = new HashMap<>();
            for (String key : keys) {
                properties.put(key, getValue(key));
            }
            return properties;
        }

        public Map<String, String> getPropertiesWithDefault(
                Map<String, String> keyDefaultValueMap) {
            Map<String, String> properties = new HashMap<>();
            for (String key : keyDefaultValueMap.keySet()) {
                properties.put(key, getValue(key, keyDefaultValueMap.get(key)));
            }
            return properties;
        }
    }
}
