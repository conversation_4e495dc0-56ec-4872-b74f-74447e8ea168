package com.bees360.flink.serialization;

import com.alibaba.fastjson.JSON;
import com.bees360.flink.entity.ProjectId;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.nio.charset.StandardCharsets;

public class KafkaProjectSerializationSchema<T extends ProjectId>
        implements KafkaRecordSerializationSchema<T> {

    private final String topic;

    public KafkaProjectSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            T project, KafkaSinkContext kafkaSinkContext, Long aLong) {
        return new ProducerRecord<>(
                topic,
                null, // choosing not to specify the partition
                serializeKey(project.getProjectId()),
                serializeValue(project));
    }

    private byte[] serializeKey(Long key) {
        return String.valueOf(key).getBytes(StandardCharsets.UTF_8);
    }

    private byte[] serializeValue(T object) {
        return JSON.toJSONString(object).getBytes(StandardCharsets.UTF_8);
    }
}
