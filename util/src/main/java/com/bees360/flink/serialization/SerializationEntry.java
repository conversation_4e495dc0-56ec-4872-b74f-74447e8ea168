package com.bees360.flink.serialization;

import lombok.Data;

import org.apache.commons.collections.KeyValue;
import org.apache.commons.collections.keyvalue.DefaultKeyValue;

import java.io.Serializable;
import java.util.Map;

@Data
public class SerializationEntry extends DefaultKeyValue implements Serializable {

    public SerializationEntry() {}

    public SerializationEntry(Object key, Object value) {
        super(key, value);
    }

    public SerializationEntry(KeyValue pair) {
        super(pair);
    }

    public SerializationEntry(Map.Entry entry) {
        super(entry);
    }
}
