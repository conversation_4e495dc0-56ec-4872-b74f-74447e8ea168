package com.bees360.flink.entity;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bees360.flink.util.TimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.flink.types.Row;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Data
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
@ToString(callSuper = true)
public class ProjectUser extends ProjectId implements Serializable {

    @JSONField(serialzeFeatures = {SerializerFeature.WriteNullListAsEmpty})
    private List<ProjectMember> projectMember;

    @JSONField(serialzeFeatures = {SerializerFeature.WriteNullListAsEmpty})
    private List<ProjectTag> projectTag;

    public static ProjectUser getProject(Row e) {
        var projectId = (Long) e.getField("id");
        var projectMember = (HashMap<Row, Integer>) e.getField("project_member");
        var projectTag = (HashMap<Row, Integer>) e.getField("project_tag");

        var project = new ProjectUser();
        project.setProjectId(projectId);

        if (projectMember != null) {
            var list =
                    projectMember.keySet().stream()
                            .map(ProjectUser::getProjectMember)
                            .collect(Collectors.toList());
            project.setProjectMember(list);
        }

        if (projectTag != null) {
            var list =
                    projectTag.keySet().stream()
                            .map(ProjectUser::getProjectTag)
                            .collect(Collectors.toList());
            project.setProjectTag(list);
        }

        return project;
    }

    public static ProjectMember getProjectMember(Row e) {
        if (e == null) {
            return null;
        }

        var in = new ProjectMember();
        String userId = (String) e.getField(0);
        in.setUserId(userId == null ? null : Long.parseLong(userId));
        String exUserId = (String) e.getField(1);
        in.setExUserId(exUserId == null ? userId : exUserId);
        in.setName((String) e.getField(2));
        in.setEmail((String) e.getField(3));
        in.setPhone((String) e.getField(4));
        in.setPhoto((String) e.getField(5));
        in.setRoleId((Integer) e.getField(6));
        in.setUpdatedBy((String) e.getField(7));
        in.setUpdatedAt(TimeUtils.ToLocalDateTimeGeneric(e.getField(8)));
        in.setDeleted((Boolean) e.getField(9));
        return in;
    }

    public static ProjectTag getProjectTag(Row e) {
        if (e == null) {
            return null;
        }

        var in = new ProjectTag();
        in.setTagId((Long) e.getField(0));
        in.setTagTitle((String) e.getField(1));
        in.setUpdatedAt(TimeUtils.ToLocalDateTimeGeneric(e.getField(4)));
        in.setUpdatedBy((String) e.getField(5));
        in.setUpdatedVia((String) e.getField(6));
        in.setDeleted((Boolean) e.getField(7));

        var companyTag = new ProjectTag.CompanyTag();
        companyTag.setCompanyId((Long) e.getField(2));
        companyTag.setTagType((Integer) e.getField(3));
        in.setCompanyTag(companyTag);

        return in;
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class ProjectMember {
        private Long userId;
        private String exUserId;
        private String name;
        private String email;
        private String phone;
        private String photo;
        private Integer roleId;
        private String updatedBy;
        private Boolean deleted;

        @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss'+00:00'")
        private LocalDateTime updatedAt;
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class ProjectTag {
        private Long tagId;
        private String tagTitle;

        @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss'+00:00'")
        private LocalDateTime updatedAt;

        private String updatedBy;
        private String updatedVia;
        private CompanyTag companyTag;
        private Boolean deleted;

        @JSONType(naming = PropertyNamingStrategy.SnakeCase)
        @Data
        public static class CompanyTag {
            private Long companyId;
            private Integer tagType;
        }
    }
}
