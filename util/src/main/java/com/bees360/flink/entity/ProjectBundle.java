package com.bees360.flink.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.serializer.SerializerFeature;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.flink.types.Row;

@EqualsAndHashCode(callSuper = true)
@Data
@JSONType(
        naming = PropertyNamingStrategy.SnakeCase,
        serialzeFeatures = {
            SerializerFeature.WriteMapNullValue,
            SerializerFeature.WriteNullListAsEmpty
        })
@ToString(callSuper = true)
public class ProjectBundle extends ProjectId implements Serializable {

    private Long projectId;
    private Bundle bundle;

    public static ProjectBundle fromRow(Row row) {
        if (row == null) return null;
        ProjectBundle pb = new ProjectBundle();
        pb.setProjectId((Long) row.getField(0));
        pb.setBundle(Bundle.fromRow(row));
        return pb;
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class Bundle implements Serializable {
        private Long id;
        private Integer serviceType;
        private String policyNo;
        private String inspectionNo;
        private String state;
        private String status;
        private Metadata metadata;
        private Address address;
        private ContractInfo contract;
        private List<BundleContact> contact;

        public static Bundle fromRow(Row row) {
            if (row == null) return null;
            Bundle b = new Bundle();
            Row bundleRow = (Row) row.getField(1);
            b.setId((Long) bundleRow.getField(0));
            b.setServiceType((Integer) bundleRow.getField(1));
            b.setPolicyNo((String) bundleRow.getField(2));
            b.setInspectionNo((String) bundleRow.getField(3));
            b.setState((String) bundleRow.getField(4));
            b.setStatus((String) bundleRow.getField(5));
            // Parse metadata from JSON string to Metadata object
            String metadataJson = (String) bundleRow.getField(6);
            if (metadataJson != null && !metadataJson.trim().isEmpty()) {
                try {
                    Metadata metadata = JSON.parseObject(metadataJson, Metadata.class);
                    b.setMetadata(metadata);
                } catch (Exception e) {
                    // If parsing fails, set to null or create empty metadata
                    b.setMetadata(new Metadata());
                }
            } else {
                b.setMetadata(new Metadata());
            }
            b.setAddress(Address.fromRow((Row) row.getField(2)));
            var bundleInsureBy = CustomerInfo.fromRow((Row) row.getField(3));
            var bundleProcessedBy = CustomerInfo.fromRow((Row) row.getField(4));
            var bundleContract = new ContractInfo(bundleInsureBy, bundleProcessedBy);
            b.setContract(bundleContract);
            var contacts = (HashMap<Row, Integer>) row.getField(5);
            if (contacts != null) {
                List<BundleContact> list =
                        contacts.keySet().stream()
                                .map(BundleContact::fromRow)
                                .collect(Collectors.toList());
                b.setContact(list);
            }
            return b;
        }
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class Address implements Serializable {
        private Long id;
        private Double lat;
        private Double lng;
        private String address;
        private String streetNumber;
        private String route;
        private String city;
        private String county;
        private String state;
        private String country;
        private String zip;
        private String streetAddress;

        public static Address fromRow(Row row) {
            if (row == null) return null;
            Address a = new Address();
            a.setId((Long) row.getField(0));
            a.setLat((Double) row.getField(1));
            a.setLng((Double) row.getField(2));
            a.setAddress((String) row.getField(3));
            a.setStreetNumber((String) row.getField(4));
            a.setRoute((String) row.getField(5));
            a.setCity((String) row.getField(6));
            a.setCounty((String) row.getField(7));
            a.setState((String) row.getField(8));
            a.setCountry((String) row.getField(9));
            a.setZip((String) row.getField(10));
            a.setStreetAddress((String) row.getField(11));
            return a;
        }
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class ContractInfo implements Serializable {
        private CustomerInfo insuredBy;
        private CustomerInfo processedBy;

        public ContractInfo(CustomerInfo insuredBy, CustomerInfo processedBy) {
            this.insuredBy = insuredBy;
            this.processedBy = processedBy;
        }
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class CustomerInfo implements Serializable {
        private Long id;
        private String name;
        private String key;
        private String logo;

        public static CustomerInfo fromRow(Row row) {
            if (row == null) return null;
            CustomerInfo c = new CustomerInfo();
            c.setId((Long) row.getField(0));
            c.setName((String) row.getField(1));
            c.setKey((String) row.getField(2));
            c.setLogo((String) row.getField(3));
            return c;
        }
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class BundleContact implements Serializable {
        private Long id;
        private String role;
        private Boolean isPrimary;
        private String fullName;
        private String email;
        private String phone;
        private Boolean deleted;

        public static BundleContact fromRow(Row row) {
            if (row == null) return null;
            BundleContact bc = new BundleContact();
            bc.setId((Long) row.getField(0));
            bc.setRole((String) row.getField(1));
            bc.setIsPrimary((Boolean) row.getField(2));
            bc.setFullName((String) row.getField(3));
            bc.setEmail((String) row.getField(4));
            bc.setPhone((String) row.getField(5));
            bc.setDeleted((Boolean) row.getField(6));
            return bc;
        }
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class Metadata implements Serializable {
        private String note;
        private Policy policy;
        private Integer yearBuilt;
        private Integer numberOfBuildings;
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class Policy implements Serializable {
        private String type;
        private Boolean isRenewal;
        private PolicyEffectiveDate policyEffectiveDate;
    }

    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    @Data
    public static class PolicyEffectiveDate implements Serializable {
        private Integer day;
        private Integer year;
        private Integer month;
    }
}
