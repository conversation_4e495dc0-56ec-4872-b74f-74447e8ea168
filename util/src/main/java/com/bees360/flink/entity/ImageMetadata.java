package com.bees360.flink.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bees360.flink.serialization.SerializationEntry;
import com.bees360.flink.util.TimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.opensearch.shaded.org.opensearch.action.update.UpdateRequest;
import org.apache.flink.opensearch.shaded.org.opensearch.common.xcontent.XContentType;
import org.apache.flink.opensearch.shaded.org.opensearch.script.Script;
import org.apache.flink.opensearch.shaded.org.opensearch.script.ScriptType;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.bees360.flink.util.JsonUtils.getMapByJsonString;

@EqualsAndHashCode(callSuper = true)
@Data
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
@ToString(callSuper = true)
public class ImageMetadata extends ImageId implements Serializable {
    public static final String IMAGE_ES_INDEX_NAME = "image_tag_v1";
    public static final String GROUP_PROJECT = "GROUP_PROJECT";
    private static final Logger logger = LoggerFactory.getLogger(ImageMetadata.class);
    private static final String ORIGIN_IMAGE_TYPE = "origin";
    private static final String LARGE_IMAGE_TYPE = "large";
    private static final String MIDDLE_IMAGE_TYPE = "middle";
    private static final String SMALL_IMAGE_TYPE = "small";

    // Mapping of image resource types
    private static final Map<Short, String> IMAGE_RESOURCE_TYPE_MAP =
            Map.of(
                    (short) 1, ORIGIN_IMAGE_TYPE,
                    (short) 2, LARGE_IMAGE_TYPE,
                    (short) 3, MIDDLE_IMAGE_TYPE,
                    (short) 4, SMALL_IMAGE_TYPE);

    private LocalDateTime createdTime; // Creation time of the image
    private Map<String, Set<Object>> groups; // Groups associated with the image

    public static UpdateRequest createUpdateIndexRequestByImageMetadata(ImageMetadata image) {
        // Build the script for the update request
        final String scriptSource = "ctx._source.putAll(params)";

        // Serialize the image metadata to JSON, retaining null values
        final String esJsonString =
                JSON.toJSONString(
                        image,
                        SerializerFeature.WriteMapNullValue,
                        SerializerFeature.WriteNullStringAsEmpty);

        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        try {
            params = getMapByJsonString(esJsonString);
        } catch (IllegalStateException e) {
            logger.error(
                    String.format(
                            "The esJsonString field value in the ImageMetadata"
                                    + " createUpdateIndexRequest method failed to be converted into a"
                                    + " Map object. esJsonString = '%s', error message = '%s'",
                            esJsonString, e.getMessage()));
        }

        // Build the update request
        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest
                .index(IMAGE_ES_INDEX_NAME)
                .id(image.getImageId())
                .script(new Script(ScriptType.INLINE, "painless", scriptSource, params))
                .scriptedUpsert(true)
                .upsert(esJsonString, XContentType.JSON);

        return updateRequest;
    }

    public static UpdateRequest createUpdateIndexRequest(SerializationEntry entity) {
        var value = (String) entity.getValue();
        var imageMetadata = JSON.parseObject(value, ImageMetadata.class);
        var imageAnnotation = JSON.parseObject(value, ImageAnnotation.class);
        var imageTag = JSON.parseObject(value, ImageTag.class);

        // Check for image annotation and create the corresponding update request
        if (imageAnnotation != null && imageAnnotation.getBoundingBox() != null) {
            return ImageAnnotation.createUpdateIndexRequest(imageAnnotation);
        }
        // Check for image tag and create the corresponding update request
        else if (imageTag != null && imageTag.getTagName() != null) {
            return ImageTag.createUpdateIndexRequest(imageTag);
        }
        // Check for image metadata and create the corresponding update request
        else if (imageMetadata != null && imageMetadata.getGroups() != null) {
            return createUpdateIndexRequestByImageMetadata(imageMetadata);
        }

        // Default update request if no specific request is created
        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest
                .index(IMAGE_ES_INDEX_NAME)
                .id(String.valueOf(entity.getKey()))
                .doc(value, XContentType.JSON)
                .docAsUpsert(true);

        return updateRequest;
    }

    public static ImageMetadata getImageMetadata(Row e) {
        var imageId = (String) e.getField("image_id");
        var createdTime = TimeUtils.ToLocalDateTimeGeneric(e.getField("created_time"));
        var groupItems = (HashMap<Row, Integer>) e.getField("group_items");

        // Return an empty ImageMetadata if imageId is null
        if (imageId == null) {
            return new ImageMetadata();
        }

        var image = new ImageMetadata();
        image.setImageId(imageId);
        image.setCreatedTime(createdTime);

        Map<String, Set<Object>> groups = new LinkedHashMap<>();
        if (groupItems != null) {
            groups =
                    groupItems.keySet().stream()
                            .collect(
                                    Collectors.groupingBy(
                                            r ->
                                                    convertGroupTypeStrToSnakeCase(
                                                            (String) r.getField(0)),
                                            Collectors.mapping(
                                                    r -> {
                                                        var groupType = (String) r.getField(0);
                                                        var groupId = (String) r.getField(1);
                                                        // Parse group ID based on type
                                                        if (GROUP_PROJECT.equals(groupType)) {
                                                            return Integer.parseInt(groupId);
                                                        }
                                                        return groupId;
                                                    },
                                                    Collectors.<Object>toSet())));
        }
        image.setGroups(groups);
        return image;
    }

    private static String convertGroupTypeStrToSnakeCase(String str) {
        // Convert the group type string to lowercase
        return StringUtils.lowerCase(str);
    }
}
