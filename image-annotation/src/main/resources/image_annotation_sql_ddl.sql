CREATE TABLE image_tag
(
    id          INT,
    title       string,
    category    string,
    description string,
    is_deleted  boolean,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
      'connector' = 'postgres-cdc',
      'decoding.plugin.name' = 'pgoutput',
      'hostname' = '$hostname',
      'port' = '$port',
      'username' = '$username',
      'password' = '$password',
      'database-name' = '$database-name',
      'schema-name' = '$schema-name',
      'table-name' = 'image_tag',
      'slot.name' = 'image_annotation_image_tag$slot-version',
      'debezium.publication.name' = 'publication_image_tag',
      'debezium.publication.autocreate.mode' = 'filtered'
      );


CREATE TABLE image_annotation
(
    id                   bigint,
    image_id             string,
    tag_id               int,
    polygon_string       string,
    created_by           string,
    created_at timestamp,
    is_deleted           boolean,
    origin_annotation_id bigint,
    source_type          smallint,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
      'connector' = 'postgres-cdc',
      'decoding.plugin.name' = 'pgoutput',
      'hostname' = '$hostname',
      'port' = '$port',
      'username' = '$username',
      'password' = '$password',
      'database-name' = '$database-name',
      'schema-name' = '$schema-name',
      'table-name' = 'image_annotation',
      'slot.name' = 'image_annotation_image_annotation$slot-version',
      'debezium.publication.name' = 'publication_image_annotation',
      'debezium.publication.autocreate.mode' = 'filtered'
      );